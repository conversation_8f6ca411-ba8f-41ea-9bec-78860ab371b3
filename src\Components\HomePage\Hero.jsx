import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaArrowRight } from "react-icons/fa";
import { useSelector } from 'react-redux';
// import DelayedModalForm from '../Common/DelayedModalForm';

// Import hero background images
import heroImg1 from '../../assets/Images/About1.webp';
import heroImg2 from '../../assets/Images/About2.webp';
import heroImg3 from '../../assets/Images/About3.webp';

export const Hero = () => {
  const { theme } = useSelector((state) => state.theme || { theme: 'dark' });
  const [currentSlide, setCurrentSlide] = useState(0);

  // Slides for the hero carousel
  const slides = [
    {
      title: "Trust in Our Expert Interior Design",
      subtitle: "JOIN  US FOR AN UNFORGETTABLE EXPERIENCE",
      description: "Discover amazing spaces with top professionals",
      backgroundImage: heroImg1
    },
    {
      title: "Transform Your Space Into A Dream Home",
      subtitle: "PREMIUM INTERIOR SOLUTIONS",
      description: "Experience the perfect blend of aesthetics and functionality",
      backgroundImage: heroImg2
    },
    {
      title: "Crafting Beautiful Spaces",
      subtitle: "DESIGNED WITH PASSION",
      description: "Where your vision meets our expertise",
      backgroundImage: heroImg3
    }
  ];

  // Auto-rotate slides
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [slides.length]);

  // Dynamic classes based on theme
  const buttonClass = `font-sans font-medium px-4 py-3 sm:px-6 sm:py-3 md:px-8 md:py-4 rounded transition-all duration-300 inline-flex items-center gap-2 text-sm sm:text-base ${
    theme === 'light'
      ? 'bg-gold-400 text-white hover:bg-gold-500'
      : 'bg-gold-300 text-richblack-900 hover:bg-gold-400'
  }`;

  const secondaryButtonClass = `font-sans font-medium px-4 py-3 sm:px-6 sm:py-3 md:px-8 md:py-4 rounded border-2 transition-all duration-300 inline-flex items-center gap-2 text-sm sm:text-base ${
    theme === 'light'
      ? 'border-gold-400 text-gold-600 hover:bg-gold-50'
      : 'border-gold-300 text-gold-300 hover:bg-richblack-800'
  }`;

  return (
    <section className="relative w-full h-screen overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute top-0 left-0 w-full h-full bg-cover bg-center bg-no-repeat transition-all duration-1000"
        style={{ backgroundImage: `url(${slides[currentSlide].backgroundImage})` }}
      />

      {/* Overlay */}
      <div className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-60" />

      {/* Content */}
      <div className="relative z-10 h-full flex flex-col items-center justify-center px-4 sm:px-6 md:px-8 lg:px-16 text-center text-white">
        <div className="max-w-4xl mx-auto w-full">
          {/* Subtitle */}
          <p className="font-sans tracking-widest text-xs sm:text-sm md:text-base mb-3 sm:mb-4 text-gold-300">
            {slides[currentSlide].subtitle}
          </p>

          {/* Main Heading */}
          <h1 className="font-inter text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 sm:mb-6 leading-tight">
            {slides[currentSlide].title}
          </h1>

          {/* Description */}
          <p className="font-sans text-sm sm:text-base md:text-lg lg:text-xl mb-6 sm:mb-8 md:mb-10 max-w-2xl mx-auto px-4">
            {slides[currentSlide].description}
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4">
            <Link to="/estimate" className="w-full sm:w-auto">
              <button className={`${buttonClass} w-full sm:w-auto justify-center`}>
                GET A QUOTE
                <FaArrowRight className="text-xs sm:text-sm" />
              </button>
            </Link>

            <Link to="/contact" className="w-full sm:w-auto">
              <button className={`${secondaryButtonClass} w-full sm:w-auto justify-center`}>
                CONTACT US
              </button>
            </Link>
          </div>

          {/* Slide Indicators */}
          <div className="flex justify-center gap-2 mt-8 sm:mt-10 md:mt-12">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 ${
                  currentSlide === index
                    ? 'bg-gold-300 w-6 sm:w-8'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-75'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Delayed Modal Form - Temporarily disabled for debugging */}
      {/* <DelayedModalForm theme={theme} delayTime={4000} /> */}
    </section>
  );
};
