@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
  }
  
  /* Responsive typography */
  h1 {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl;
  }
  
  h2 {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl;
  }
  
  h3 {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl;
  }
  
  p {
    @apply text-sm sm:text-base md:text-lg;
  }
  
  /* Responsive spacing utilities */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-8 sm:py-12 md:py-16 lg:py-20;
  }
  
  .mobile-padding {
    @apply px-4 sm:px-6 md:px-8;
  }
}

@layer components {
  /* Responsive button components */
  .btn-primary {
    @apply px-4 py-2 sm:px-6 sm:py-3 md:px-8 md:py-4 text-sm sm:text-base font-medium rounded transition-all duration-300;
  }
  
  .btn-secondary {
    @apply px-3 py-2 sm:px-5 sm:py-3 md:px-6 md:py-3 text-sm sm:text-base font-medium rounded transition-all duration-300;
  }
  
  /* Responsive card components */
  .card-responsive {
    @apply bg-richblack-800 rounded-lg p-4 sm:p-6 md:p-8 shadow-lg hover:shadow-xl transition-all duration-300;
  }
  
  /* Responsive grid layouts */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 md:gap-8;
  }
  
  .grid-responsive-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 lg:gap-12;
  }
  
  .grid-responsive-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8;
  }
  
  /* Responsive form components */
  .form-input-responsive {
    @apply w-full px-3 py-2 sm:px-4 sm:py-3 text-sm sm:text-base rounded-md bg-richblack-800 text-white placeholder-richblack-400 focus:outline-none focus:ring-2 focus:ring-gold-500 transition-all duration-300;
  }
  
  .form-textarea-responsive {
    @apply w-full px-3 py-2 sm:px-4 sm:py-3 text-sm sm:text-base rounded-md bg-richblack-800 text-white placeholder-richblack-400 focus:outline-none focus:ring-2 focus:ring-gold-500 transition-all duration-300 resize-none;
  }
  
  /* Responsive navigation */
  .nav-link-responsive {
    @apply text-sm sm:text-base font-medium transition-colors duration-300;
  }
  
  /* Responsive image containers */
  .image-container-responsive {
    @apply relative overflow-hidden rounded-lg;
  }
  
  .image-responsive {
    @apply w-full h-48 sm:h-56 md:h-64 lg:h-72 object-cover transition-transform duration-500;
  }
  
  /* Responsive text utilities */
  .text-responsive-sm {
    @apply text-xs sm:text-sm md:text-base;
  }
  
  .text-responsive-base {
    @apply text-sm sm:text-base md:text-lg;
  }
  
  .text-responsive-lg {
    @apply text-base sm:text-lg md:text-xl;
  }
  
  /* Responsive spacing utilities */
  .space-responsive {
    @apply space-y-4 sm:space-y-6 md:space-y-8;
  }
  
  .gap-responsive {
    @apply gap-4 sm:gap-6 md:gap-8;
  }
  
  /* Mobile-first responsive utilities */
  .mobile-hidden {
    @apply hidden sm:block;
  }
  
  .mobile-only {
    @apply block sm:hidden;
  }
  
  .tablet-hidden {
    @apply hidden md:block;
  }
  
  .tablet-only {
    @apply block md:hidden;
  }
  
  .desktop-hidden {
    @apply hidden lg:block;
  }
  
  .desktop-only {
    @apply block lg:hidden;
  }
}

/* Custom animations */
@keyframes slide-in-right {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  70% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse-attention {
  0% {
    box-shadow: 0 0 0 0 rgba(212, 169, 64, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(212, 169, 64, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(212, 169, 64, 0);
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out forwards;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.5s ease-out forwards;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out forwards;
}

.animate-pulse-attention {
  animation: pulse-attention 2s infinite;
}

/* Responsive scrollbar */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 4px;
  }
  
  ::-webkit-scrollbar-track {
    background: #1e293b;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #d4a940;
    border-radius: 2px;
  }
}

/* Touch-friendly interactions for mobile */
@media (max-width: 768px) {
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Responsive focus states */
@media (max-width: 768px) {
  .focus\:ring-2:focus {
    outline: 2px solid #d4a940;
    outline-offset: 2px;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}
