import { useState } from 'react';
import "slick-carousel/slick/slick.css";
import Slider from "react-slick";
import "slick-carousel/slick/slick-theme.css";
import { FaQuoteLeft, FaQuoteRight, FaStar, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

// Updated testimonials with interior design context and ratings
const testimonials = [
  {
    project: "Complete Home Renovation",
    projectImage: "https://picsum.photos/id/1067/600/400",
    quote:
      "The team transformed our outdated home into a modern masterpiece. Their attention to detail and ability to understand our vision exceeded our expectations. The project was completed on time and within budget.",
    name: "<PERSON><PERSON>",
    location: "Mumbai, Maharashtra",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/women/32.jpg",
    date: "March 2023"
  },
  {
    project: "Kitchen Remodeling",
    projectImage: "https://picsum.photos/id/1062/600/400",
    quote:
      "Our kitchen renovation was handled with exceptional professionalism. The designers created a functional yet stylish space that has become the heart of our home. The quality of workmanship is outstanding.",
    name: "<PERSON><PERSON>",
    location: "Bangalore, Karnataka",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/men/45.jpg",
    date: "January 2023"
  },
  {
    project: "Living Room Design",
    projectImage: "https://picsum.photos/id/1054/600/400",
    quote:
      "The living room design perfectly captures our personality while maintaining elegance and comfort. The team's creativity and expertise in selecting furniture, colors, and accessories created a cohesive and inviting space.",
    name: "Ananya Patel",
    location: "Delhi, NCR",
    rating: 4.5,
    avatar: "https://randomuser.me/api/portraits/women/77.jpg",
    date: "February 2023"
  },
  {
    project: "Master Bedroom Suite",
    projectImage: "https://picsum.photos/id/1056/600/400",
    quote:
      "Our master bedroom suite is now a luxurious retreat thanks to the incredible design team. They incorporated our preferences while adding unexpected elements that elevated the entire space. Truly a 5-star experience.",
    name: "Vikram Singh",
    location: "Pune, Maharashtra",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/men/22.jpg",
    date: "April 2023"
  },
];

// Custom arrow components
const PrevArrow = (props) => {
  const { onClick } = props;
  return (
    <button
      onClick={onClick}
      className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 z-10 bg-gold-500 text-richblack-900 p-3 rounded-full shadow-lg hover:bg-gold-400 transition-all duration-300 hidden lg:block"
      aria-label="Previous slide"
    >
      <FaChevronLeft />
    </button>
  );
};

const NextArrow = (props) => {
  const { onClick } = props;
  return (
    <button
      onClick={onClick}
      className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 z-10 bg-gold-500 text-richblack-900 p-3 rounded-full shadow-lg hover:bg-gold-400 transition-all duration-300 hidden lg:block"
      aria-label="Next slide"
    >
      <FaChevronRight />
    </button>
  );
};

export const Testimonail = () => {
  const [activeSlide, setActiveSlide] = useState(0);

  // Render star rating
  const renderRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<FaStar key={i} className="text-gold-500" />);
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<FaStar key={i} className="text-gold-500 opacity-50" />);
      } else {
        stars.push(<FaStar key={i} className="text-gray-600" />);
      }
    }
    
    return <div className="flex">{stars}</div>;
  };

  const settings = {
    arrows: true,
    prevArrow: <PrevArrow />,
    nextArrow: <NextArrow />,
    infinite: true,
    speed: 800,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 6000,
    fade: true,
    beforeChange: (current, next) => setActiveSlide(next),
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  return (
    <section className="py-20 bg-richblack-900 text-white relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute -right-40 -top-40 w-80 h-80 rounded-full bg-gold-500"></div>
        <div className="absolute -left-20 -bottom-20 w-60 h-60 rounded-full bg-gold-500"></div>
      </div>
      
      <div className="max-w-6xl mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="mb-16 text-center">
          <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center">
            <span className="w-10 h-1 bg-gold-500 mr-4 inline-block"></span>
            Client Testimonials
            <span className="w-10 h-1 bg-gold-500 ml-4 inline-block"></span>
          </h2>
          <p className="text-gray-300 max-w-3xl mx-auto">
            Discover what our clients have to say about their experience working with our interior design team
          </p>
        </div>
        
        {/* Testimonial Slider */}
        <div className="relative">
          <Slider {...settings} className="testimonial-slider">
            {testimonials.map((item, index) => (
              <div key={index} className="outline-none">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                  {/* Project Image */}
                  <div className="relative h-[300px] lg:h-[400px] rounded-lg overflow-hidden shadow-xl">
                    <img 
                      src={item.projectImage} 
                      alt={`${item.project} by ${item.name}`} 
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 p-6">
                      <h3 className="text-xl font-bold text-white mb-2">{item.project}</h3>
                      <p className="text-gray-300 text-sm">{item.location}</p>
                    </div>
                  </div>
                  
                  {/* Testimonial Content */}
                  <div className="bg-richblack-800 rounded-lg p-8 shadow-xl relative">
                    <FaQuoteLeft className="text-gold-500/20 text-4xl absolute top-4 left-4" />
                    
                    <div className="mb-6 relative z-10">
                      <div className="flex items-center gap-2 mb-4">
                        {renderRating(item.rating)}
                        <span className="text-gold-400 text-sm ml-2">{item.rating.toFixed(1)}</span>
                      </div>
                      
                      <p className="text-lg text-gray-200 leading-relaxed relative z-10">
                        "{item.quote}"
                      </p>
                    </div>
                    
                    <div className="flex items-center gap-4 mt-8 border-t border-richblack-700 pt-6">
                      <img 
                        src={item.avatar} 
                        alt={item.name} 
                        className="w-16 h-16 rounded-full object-cover border-2 border-gold-500/30"
                      />
                      <div>
                        <p className="font-bold text-white text-lg">{item.name}</p>
                        <p className="text-gray-400">{item.location}</p>
                        <p className="text-gold-500 text-sm mt-1">{item.date}</p>
                      </div>
                    </div>
                    
                    <FaQuoteRight className="text-gold-500/20 text-4xl absolute bottom-4 right-4" />
                  </div>
                </div>
              </div>
            ))}
          </Slider>
          
          {/* Testimonial Navigation Dots */}
          <div className="flex justify-center gap-3 mt-10">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  const slider = document.querySelector('.testimonial-slider');
                  if (slider && slider.slick) {
                    slider.slick.slickGoTo(index);
                  }
                }}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  activeSlide === index ? 'bg-gold-500 w-8' : 'bg-gray-600'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
        
        {/* Call to Action */}
        <div className="mt-16 text-center">
          <p className="text-lg text-gray-300 mb-6">
            Join our growing list of satisfied clients and experience the difference of working with a professional interior design team.
          </p>
          <a
            href="#contact"
            className="inline-flex items-center gap-2 px-8 py-3 bg-gold-500 hover:bg-gold-400 text-richblack-900 font-medium rounded-sm transition-all duration-300"
          >
            Schedule a Consultation
          </a>
        </div>
      </div>
    </section>
  );
};
