
import { Link } from "react-router-dom";
import Logo from "../../assets/logo/Engraved_Gold_&_Silver_01[1](1).png"
import { FaFacebook, FaInstagram, FaPinterest, FaYoutube } from "react-icons/fa";

const BottomFooter = ["Privacy Policy", "Terms of Service", "Contact Us"];
const Services = ["Residential Design", "Commercial Design", "Space Planning", "Consultations"];
const Portfolio = ["Projects", "Gallery", "Before & After"];
const Company = ["About Us", "Team", "Careers"];
const Community = ["Testimonials", "Blog", "Events"];
const GetInspired = ["Mood Boards", "Material Guides", "Design Tips"];

export const Footer = () => {
  return (
    <footer className="bg-richblack-800 text-richblack-400 font-inter">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16">
          {/* Company Section */}
          <div className="space-y-6">
            <img
              src={Logo}
              alt="Interior Company Logo"
              className="h-12 w-auto"
            />
            <p className="text-sm leading-relaxed max-w-xs">
              India's Best Interior Design Company specializing in complete interior packages, modular solutions, and turnkey construction projects.
            </p>
            <div className="flex space-x-4">
              <SocialIcon Icon={FaFacebook} href="https://www.facebook.com/sparrowinterior/" />
              <SocialIcon Icon={FaInstagram} href="https://www.instagram.com/sparrowinteriors.in/" />
              <SocialIcon Icon={FaYoutube} href="https://x.com/SparrowInter" />
            </div>
          </div>

          {/* Quick Links Section */}
          <div className="space-y-6">
            <FooterSection title="Services" items={Services} />
            <FooterSection title="Portfolio" items={Portfolio} />
          </div>

          {/* Contact Information */}
          <div className="space-y-6">
            <h3 className="text-richblack-50 font-semibold text-lg">Contact Us</h3>
            <div className="space-y-4">
              <ContactItem icon="📞" text="9599222158" />
              <ContactItem
                icon="✉️"
                text="<EMAIL>"
                href="mailto:<EMAIL>"
              />
              <ContactItem
                icon="📍"
                text="E-212, Basement, Sector -63, Noida 201301"
              />
            </div>
          </div>

          {/* Newsletter Section */}
          <div className="space-y-6">
            <h3 className="text-richblack-50 font-semibold text-lg">Stay Updated</h3>
            <p className="text-sm">Subscribe to our newsletter for design inspiration and updates.</p>
            <form className="space-y-3">
              <input
                type="email"
                placeholder="Enter your email"
                className="w-full px-4 py-3 rounded-lg bg-richblack-700 text-white placeholder-richblack-400 focus:outline-none focus:ring-2 focus:ring-yellow-500"
              />
              <button className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-300">
                Subscribe
              </button>
            </form>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-richblack-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-wrap justify-center gap-6">
              {BottomFooter.map((item) => (
                <Link
                  key={item}
                  to={item.toLowerCase().replace(/\s/g, "-")}
                  className="text-sm hover:text-richblack-50 transition-colors duration-200"
                >
                  {item}
                </Link>
              ))}
            </div>
            <p className="text-sm text-center">
              © {new Date().getFullYear()} Sparrow Interior. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

// Helper Components
const SocialIcon = ({ Icon, href }) => (
  <a
    href={href}
    target="_blank"
    rel="noopener noreferrer"
    className="text-richblack-400 hover:text-yellow-500 transition-colors duration-300"
  >
    <Icon className="w-5 h-5" />
  </a>
);

const FooterSection = ({ title, items }) => (
  <div>
    <h3 className="text-richblack-50 font-semibold text-lg mb-4">{title}</h3>
    <ul className="space-y-2">
      {items.map((item) => (
        <li key={item}>
          <Link
            to={item.toLowerCase().replace(/\s/g, "-")}
            className="text-sm hover:text-richblack-50 transition-colors duration-200"
          >
            {item}
          </Link>
        </li>
      ))}
    </ul>
  </div>
);

const ContactItem = ({ icon, text, href }) => (
  <div className="flex items-center space-x-2">
    <span>{icon}</span>
    {href ? (
      <a href={href} className="text-sm hover:text-richblack-50 transition-colors duration-200">
        {text}
      </a>
    ) : (
      <span className="text-sm">{text}</span>
    )}
  </div>
);
