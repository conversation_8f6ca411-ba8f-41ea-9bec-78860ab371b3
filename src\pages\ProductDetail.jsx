import { useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import Navbar from '../Components/Common/Navbar';
import { Footer } from '../Components/Common/Footer';
import Breadcrumb from '../Components/Common/Breadcrumb';
import { FaArrowLeft, FaStar, FaHeart, FaRegHeart, FaShare, FaTimes, FaWhatsapp, FaPhone } from 'react-icons/fa';

// Import product images
import sofaImg from '../assets/Images/furniture_selection.jpeg';
import lightImg from '../assets/Images/custom_interior_design.jpeg';
import artImg from '../assets/Images/About1.webp';
import tableImg from '../assets/Images/About2.webp';
import wardrobeImg from '../assets/Images/space_planning.jpeg';
import lampImg from '../assets/Images/home_renovation.jpeg';
import headerImg1 from '../assets/Images/Header_Image/237389.jpg';
import headerImg2 from '../assets/Images/Header_Image/242858.jpg';
import headerImg3 from '../assets/Images/Header_Image/25131.jpg';

const ProductDetail = () => {
  const { slug } = useParams();
  const [selectedImage, setSelectedImage] = useState(0);
  const [showPriceForm, setShowPriceForm] = useState(false);
  const [isWishlisted, setIsWishlisted] = useState(false);

  // Mock product data - in real app, this would come from API
  const productData = {
    'modern-scandinavian-sofa': {
      name: 'Modern Scandinavian Sofa',
      category: 'Seating',
      rating: 4.8,
      reviewCount: 124,
      description: 'Luxurious modern Scandinavian sofa with premium upholstery and solid wood frame. Perfect for contemporary living spaces with its clean lines and comfortable seating.',
      features: [
        'Premium fabric upholstery',
        'Solid hardwood frame',
        'High-density foam cushions',
        'Removable cushion covers',
        'Anti-sag spring system',
        'Stain-resistant fabric'
      ],
      specifications: {
        'Dimensions': 'L: 210cm × W: 85cm × H: 80cm',
        'Material': 'Fabric upholstery, Hardwood frame',
        'Color Options': 'Beige, Grey, Navy Blue',
        'Weight': '45 kg',
        'Assembly': 'Minimal assembly required',
        'Warranty': '2 years'
      },
      images: [
        sofaImg,
        tableImg,
        artImg,
        wardrobeImg
      ],
      relatedProducts: [
        { name: 'Marble Coffee Table', image: tableImg, slug: 'marble-coffee-table' },
        { name: 'Designer Floor Lamp', image: lampImg, slug: 'designer-floor-lamp' },
        { name: 'Abstract Canvas Art Set', image: artImg, slug: 'abstract-canvas-art-set' }
      ]
    },
    'geometric-brass-pendant-light': {
      name: 'Geometric Brass Pendant Light',
      category: 'Lighting',
      rating: 4.7,
      reviewCount: 86,
      description: 'Modern geometric pendant light with brass finish, perfect for dining areas and kitchen islands.',
      features: [
        'Brass finish',
        'Adjustable height',
        'E27 socket',
        'LED compatible',
        'Easy installation',
        'Modern design'
      ],
      specifications: {
        'Dimensions': 'Diameter: 35cm × H: 45cm',
        'Material': 'Brass, Metal',
        'Color Options': 'Brass, Black, Copper',
        'Weight': '2.5 kg',
        'Bulb Type': 'E27 LED (not included)',
        'Warranty': '1 year'
      },
      images: [
        lightImg,
        sofaImg,
        artImg,
        lampImg
      ],
      relatedProducts: [
        { name: 'Modern Scandinavian Sofa', image: sofaImg, slug: 'modern-scandinavian-sofa' },
        { name: 'Designer Floor Lamp', image: lampImg, slug: 'designer-floor-lamp' },
        { name: 'Minimalist Wooden Wardrobe', image: wardrobeImg, slug: 'minimalist-wooden-wardrobe' }
      ]
    },
    'abstract-canvas-art-set': {
      name: 'Abstract Canvas Art Set',
      category: 'Wall Art',
      rating: 4.5,
      reviewCount: 52,
      description: 'Set of 3 abstract canvas prints with gold accents, perfect for modern interiors. Each piece is carefully crafted to create a cohesive artistic statement.',
      features: [
        'Set of 3 canvas prints',
        'Gold accent details',
        'Ready to hang',
        'Wooden frame included',
        'UV-resistant inks',
        'Modern abstract design'
      ],
      specifications: {
        'Dimensions': '60cm × 90cm (each piece)',
        'Material': 'Canvas, Wood frame',
        'Color Options': 'Multi-color, Black & White',
        'Weight': '1.5 kg (per piece)',
        'Frame': 'Wooden frame included',
        'Warranty': '1 year'
      },
      images: [
        artImg,
        sofaImg,
        tableImg,
        wardrobeImg
      ],
      relatedProducts: [
        { name: 'Designer Floor Lamp', image: lampImg, slug: 'designer-floor-lamp' },
        { name: 'Modern Scandinavian Sofa', image: sofaImg, slug: 'modern-scandinavian-sofa' },
        { name: 'Marble Coffee Table', image: tableImg, slug: 'marble-coffee-table' }
      ]
    },
    'marble-coffee-table': {
      name: 'Marble Coffee Table',
      category: 'Tables',
      rating: 4.8,
      reviewCount: 31,
      description: 'Elegant coffee table with genuine marble top and gold-finished metal base. A perfect centerpiece for any modern living room.',
      features: [
        'Genuine marble top',
        'Gold-finished metal base',
        'Protective feet pads',
        'Easy assembly',
        'Scratch-resistant surface',
        'Contemporary design'
      ],
      specifications: {
        'Dimensions': 'L: 120cm × W: 60cm × H: 45cm',
        'Material': 'Marble top, Metal base',
        'Color Options': 'White Marble, Black Marble',
        'Weight': '35 kg',
        'Assembly': 'Required (tools included)',
        'Warranty': '2 years'
      },
      images: [
        tableImg,
        sofaImg,
        artImg,
        wardrobeImg
      ],
      relatedProducts: [
        { name: 'Modern Scandinavian Sofa', image: sofaImg, slug: 'modern-scandinavian-sofa' },
        { name: 'Abstract Canvas Art Set', image: artImg, slug: 'abstract-canvas-art-set' },
        { name: 'Designer Floor Lamp', image: lampImg, slug: 'designer-floor-lamp' }
      ]
    },
    'minimalist-wooden-wardrobe': {
      name: 'Minimalist Wooden Wardrobe',
      category: 'Storage',
      rating: 4.9,
      reviewCount: 156,
      description: 'Spacious 3-door wardrobe with modern design and ample storage space. Perfect for organizing your bedroom with style.',
      features: [
        '3-door design',
        'Ample storage space',
        'Solid wood construction',
        'Soft-close hinges',
        'Adjustable shelves',
        'Modern minimalist design'
      ],
      specifications: {
        'Dimensions': 'L: 180cm × W: 60cm × H: 200cm',
        'Material': 'Solid wood, Metal hardware',
        'Color Options': 'Natural Oak, White, Walnut',
        'Weight': '85 kg',
        'Assembly': 'Required (instructions included)',
        'Warranty': '3 years'
      },
      images: [
        wardrobeImg,
        sofaImg,
        tableImg,
        artImg
      ],
      relatedProducts: [
        { name: 'Modern Scandinavian Sofa', image: sofaImg, slug: 'modern-scandinavian-sofa' },
        { name: 'Marble Coffee Table', image: tableImg, slug: 'marble-coffee-table' },
        { name: 'Designer Floor Lamp', image: lampImg, slug: 'designer-floor-lamp' }
      ]
    },
    'designer-floor-lamp': {
      name: 'Designer Floor Lamp',
      category: 'Lighting',
      rating: 4.6,
      reviewCount: 78,
      description: 'Contemporary floor lamp with adjustable height and warm LED lighting. Perfect for reading corners and ambient lighting.',
      features: [
        'Adjustable height',
        'Warm LED lighting',
        'Touch control',
        'Dimmer function',
        'Stable base',
        'Contemporary design'
      ],
      specifications: {
        'Dimensions': 'Base: 30cm × H: 150-180cm (adjustable)',
        'Material': 'Metal, Fabric shade',
        'Color Options': 'Black, White, Brass',
        'Weight': '8 kg',
        'Bulb Type': 'LED (included)',
        'Warranty': '2 years'
      },
      images: [
        lampImg,
        lightImg,
        sofaImg,
        artImg
      ],
      relatedProducts: [
        { name: 'Geometric Brass Pendant Light', image: lightImg, slug: 'geometric-brass-pendant-light' },
        { name: 'Modern Scandinavian Sofa', image: sofaImg, slug: 'modern-scandinavian-sofa' },
        { name: 'Abstract Canvas Art Set', image: artImg, slug: 'abstract-canvas-art-set' }
      ]
    }
  };

  const product = productData[slug] || productData['modern-scandinavian-sofa'];

  const renderRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <FaStar 
          key={i} 
          className={i <= fullStars ? "text-yellow-500" : "text-gray-400"} 
        />
      );
    }
    return <div className="flex">{stars}</div>;
  };

  const handleRequestPrice = () => {
    setShowPriceForm(true);
  };

  return (
    <div className="bg-richblack-900 min-h-screen">
      <Navbar />
      <Breadcrumb />

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Back Button */}
        <Link
          to="/store"
          className="inline-flex items-center gap-2 text-gold-400 hover:text-gold-300 transition-colors duration-300 mb-6"
        >
          <FaArrowLeft /> Back to Store
        </Link>

        {/* Product Details */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          {/* Product Images */}
          <div>
            {/* Main Image */}
            <div className="mb-4">
              <img
                src={product.images[selectedImage]}
                alt={product.name}
                className="w-full h-96 object-cover rounded-lg"
              />
            </div>
            
            {/* Thumbnail Images */}
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`border-2 rounded-lg overflow-hidden ${
                    selectedImage === index ? 'border-gold-500' : 'border-richblack-700'
                  }`}
                >
                  <img
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-20 object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div>
            <div className="mb-4">
              <span className="text-gold-400 text-sm font-medium">{product.category}</span>
            </div>
            
            <h1 className="text-3xl font-bold text-white mb-4">{product.name}</h1>
            
            {/* Rating */}
            <div className="flex items-center gap-3 mb-6">
              {renderRating(product.rating)}
              <span className="text-gray-300">({product.reviewCount} reviews)</span>
            </div>

            {/* Description */}
            <p className="text-gray-300 mb-6">{product.description}</p>

            {/* Features */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-white mb-3">Key Features</h3>
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="text-gray-300 flex items-center">
                    <span className="w-2 h-2 bg-gold-500 rounded-full mr-3"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4 mb-8">
              <button
                onClick={handleRequestPrice}
                className="flex-1 bg-gold-500 text-richblack-900 py-3 px-6 rounded-lg font-semibold hover:bg-gold-400 transition-colors duration-300"
              >
                Request Price & Details
              </button>
              <button
                onClick={() => setIsWishlisted(!isWishlisted)}
                className="p-3 border border-gold-500 text-gold-500 rounded-lg hover:bg-gold-500/10 transition-colors duration-300"
              >
                {isWishlisted ? <FaHeart /> : <FaRegHeart />}
              </button>
              <button className="p-3 border border-gold-500 text-gold-500 rounded-lg hover:bg-gold-500/10 transition-colors duration-300">
                <FaShare />
              </button>
            </div>

            {/* Contact Info */}
            <div className="bg-richblack-800 p-4 rounded-lg mb-6">
              <p className="text-white font-medium mb-2">Need Help?</p>
              <p className="text-gray-300 text-sm mb-3">Contact our design experts for personalized assistance</p>
              <div className="flex gap-3">
                <a href="tel:9599222158" className="flex items-center gap-2 text-gold-400 hover:text-gold-300 font-medium">
                  <FaPhone /> 9599222158
                </a>
                <a href="https://wa.me/919599222158" className="flex items-center gap-2 text-green-400 hover:text-green-300 font-medium">
                  <FaWhatsapp /> WhatsApp
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Specifications */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-white mb-6">Specifications</h2>
          <div className="bg-richblack-800 rounded-lg p-6">
            <div className="grid md:grid-cols-2 gap-6">
              {Object.entries(product.specifications).map(([key, value]) => (
                <div key={key} className="flex justify-between border-b border-richblack-700 pb-2">
                  <span className="text-gray-400">{key}:</span>
                  <span className="text-white">{value}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Related Products */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-white mb-6">Related Products</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {product.relatedProducts.map((relatedProduct, index) => (
              <Link
                key={index}
                to={`/store/product/${relatedProduct.slug}`}
                className="block group"
              >
                <div className="bg-richblack-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-richblack-700 hover:border-gold-500">
                  <img
                    src={relatedProduct.image}
                    alt={relatedProduct.name}
                    className="w-full h-48 object-cover transform transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-white group-hover:text-gold-400 transition-colors duration-300">
                      {relatedProduct.name}
                    </h3>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Price Request Modal */}
      {showPriceForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-richblack-800 rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-white">Request Price</h3>
              <button
                onClick={() => setShowPriceForm(false)}
                className="text-gray-400 hover:text-white"
              >
                <FaTimes />
              </button>
            </div>
            <form className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Product</label>
                <input
                  type="text"
                  value={product.name}
                  readOnly
                  className="w-full p-3 bg-richblack-700 text-white rounded border border-richblack-600"
                />
              </div>
              <div>
                <label className="block text-gray-300 mb-2">Your Name</label>
                <input
                  type="text"
                  className="w-full p-3 bg-richblack-700 text-white rounded border border-richblack-600 focus:border-gold-500 outline-none"
                  placeholder="Enter your name"
                />
              </div>
              <div>
                <label className="block text-gray-300 mb-2">Phone Number</label>
                <input
                  type="tel"
                  className="w-full p-3 bg-richblack-700 text-white rounded border border-richblack-600 focus:border-gold-500 outline-none"
                  placeholder="Enter your phone number"
                />
              </div>
              <div>
                <label className="block text-gray-300 mb-2">Email</label>
                <input
                  type="email"
                  className="w-full p-3 bg-richblack-700 text-white rounded border border-richblack-600 focus:border-gold-500 outline-none"
                  placeholder="Enter your email"
                />
              </div>
              <button
                type="submit"
                className="w-full bg-gold-500 text-richblack-900 py-3 rounded font-semibold hover:bg-gold-400 transition-colors duration-300"
              >
                Submit Request
              </button>
            </form>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default ProductDetail;
