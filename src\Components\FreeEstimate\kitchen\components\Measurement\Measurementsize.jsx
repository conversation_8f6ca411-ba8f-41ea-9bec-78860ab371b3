import React from 'react';
import { Link } from 'react-router-dom';

const Measurementsize = () => {
    return (
        <div className="max-w-xl mx-auto px-4 py-10">
            <h2 className="text-2xl sm:text-3xl font-semibold font-inter text-center text-richblack-25 mb-8">
                Now review the measurements for accuracy
            </h2>

            <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center mb-6">
                <span className="text-xl font-medium text-red-600 border border-yellow-300 rounded-md px-6 py-2 inline-block">
                    A
                </span>
            </div>

            <div className="bg-yellow-100 text-yellow-800 text-sm text-center py-2 px-4 rounded-md mb-6">
                Standard size has been set for your convenience
            </div>

            <div className="flex items-center justify-center gap-2 mb-20">
                <label htmlFor="measurement" className="text-lg font-medium text-gray-700">A</label>
                <select
                    id="measurement"
                    onChange={(e) => onChange && onChange(e.target.value)}
                    className="border border-gray-300 rounded-md px-4 py-2 text-gray-800"
                >
                    {[...Array(20)].map((_, i) => (
                        <option key={i} value={i + 1}>{i + 1}</option>
                    ))}
                </select>
                <span className="text-lg text-gray-600">ft.</span>
            </div>

            <div className="flex justify-between items-center px-4">
                <button

                    className="text-yellow-300 hover:text-red-600 font-semibold"
                >
                    BACK
                </button>
                <Link to='package'>
                    <button

                        className="bg-yellow-300 hover:bg-yellow-500 text-white px-6 py-3 rounded-full font-semibold shadow-md transition-all"
                    >
                        NEXT
                    </button>
                </Link>
            </div>
        </div>
    )
}

export default Measurementsize