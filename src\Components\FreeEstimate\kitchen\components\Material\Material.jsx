import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const materials = [
    {
        id: 'hdfhmr',
        name: 'HDFHMR',
        description: 'Has high strength and density, and a solid screw-holding capacity.',
        image: '/path-to-your-image/hdfhmr.jpg',
        price: '₹₹₹',
        tip: 'Makes for a good choice as it has load-bearing capacity at a lower cost.',
    },
    {
        id: 'mdf',
        name: 'MDF',
        description: 'Is seamless, free of knots, and has high machinability.',
        image: '/path-to-your-image/mdf.jpg',
        price: '₹₹',
        tip: 'Perfect for smooth finishes and painting.',
    },
];

const Material = () => {
    const [selected, setSelected] = useState(null);

    return (
        <div className="max-w-4xl mx-auto px-4 py-10 text-white">
            <h2 className="text-3xl font-semibold text-center mb-2 text-richblack-5 font-interior">
                Materials for cabinets and shutters. Take your pick.
            </h2>

            <div className="space-y-6">
                {materials.map((mat) => (
                    <label
                        key={mat.id}
                        className={`block border rounded-xl font-inter text-richblack-50 p-5 transition-all duration-300 shadow-md cursor-pointer ${selected === mat.id
                            ? 'border-red-600  '
                            : 'border-richblack-600 '
                            }`}
                    >
                        <div className="flex items-start gap-4">
                            <input
                                type="radio"
                                name="material"
                                value={mat.id}
                                checked={selected === mat.id}
                                onChange={() => setSelected(mat.id)}
                                className="mt-1 accent-yellow-400"
                            />
                            <div className="flex-1">
                                <p className="text-xl font-semibold">{mat.name}</p>
                                <p className="text-sm text-richblack-200">{mat.description}</p>
                                <img
                                    src={mat.image}
                                    alt={mat.name}
                                    className="w-40 mt-4 rounded-lg"
                                />
                                <div className="mt-4 space-y-1 text-sm text-richblack-200">
                                    <p>
                                        <span className="font-medium text-richblack-100">Price</span>: {mat.price}
                                    </p>
                                    <p>
                                        <span className="font-medium text-richblack-100">Pro Tip</span>: {mat.tip}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </label>
                ))}
            </div>

            <div className="flex justify-between items-center mt-12 px-4">
                <button
                    className="text-yellow-400 hover:text-yellow-500 font-semibold transition-all"
                    onClick={() => console.log('Back clicked')}
                >
                    BACK
                </button>
                <Link to='getestimate'>
                    <button
                        className={`px-6 py-3 rounded-full font-semibold shadow-md transition-all ${selected
                            ? 'bg-yellow-300 hover:bg-yellow-500 text-black'
                            : ' text-richblack-400 cursor-not-allowed'
                            }`}
                        disabled={!selected}
                        onClick={() => selected && console.log('Next clicked with:', selected)}
                    >
                        NEXT
                    </button>
                </Link>
            </div>
        </div>
    );
};

export default Material;
