import React, { useState } from 'react';
import { MinusCircle, PlusCircle } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';

export const RoomSelectionSection = () => {
    const [rooms, setRooms] = useState({
        livingRoom: 1,
        kitchen: 1,
        bedroom: 2,
        bathroom: 2,
        dining: 1,
    });
    const navigate = useNavigate();

    const roomLabels = {
        livingRoom: "Living Room",
        kitchen: "Kitchen",
        bedroom: "Bedroom",
        bathroom: "Bathroom",
        dining: "Dining",
    };

    const handleIncrement = (room) => {
        setRooms((prev) => ({
            ...prev,
            [room]: prev[room] + 1,
        }));
    };

    const handleDecrement = (room) => {
        setRooms((prev) => ({
            ...prev,
            [room]: prev[room] > 0 ? prev[room] - 1 : 0,
        }));
    };

    return (
        <div className="mt-4 bg-[#0b1120] p-4 text-richblack-25 font-inter  flex flex-col items-center justify-center">
            <div className="max-w-xl w-full">
                <h2 className="text-2xl font-semibold text-center">
                    Select the rooms you’d like us to design
                </h2>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8">
                    {Object.keys(rooms).map((roomKey) => (
                        <div
                            key={roomKey}
                            className="flex items-center justify-between p-5 bg-white/10 rounded-xl shadow-md"
                        >
                            <span className="font-medium text-white">{roomLabels[roomKey]}</span>
                            <div className="flex items-center gap-3">
                                <button
                                    onClick={() => handleDecrement(roomKey)}
                                    className="text-yellow-300 hover:text-red-500"
                                >
                                    <MinusCircle className="w-5 h-5" />
                                </button>
                                <span className="text-white">{rooms[roomKey]}</span>
                                <button
                                    onClick={() => handleIncrement(roomKey)}
                                    className="text-yellow-300 hover:text-red-500"
                                >
                                    <PlusCircle className="w-5 h-5" />
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="flex justify-between mt-10">

                    <button onClick={() => {
                        navigate(-1)
                    }
                    } className="text-sm text-yellow-300 hover:underline">BACK</button>
                    <Link to="package">
                        <button className="px-6 py-2 rounded-full bg-yellow-300 yellow-300 text-white">
                            NEXT
                        </button>
                    </Link>
                </div>
            </div>
        </div>
    );
};
