
import { Link } from 'react-router-dom';
import Navbar from '../../Components/Common/Navbar';
import { Footer } from '../../Components/Common/Footer';
import Breadcrumb from '../../Components/Common/Breadcrumb';
import { FaCheck, FaPhone, FaEnvelope, FaUtensils, FaTshirt, FaCog } from 'react-icons/fa';

// Import images
import headerImg from '../../assets/Images/Header_Image/18475940.jpg';
import dummyImage1 from '../../assets/Images/About1.webp';
import dummyImage2 from '../../assets/Images/About2.webp';
import dummyImage3 from '../../assets/Images/About3.webp';

const ModularKitchenWardrobes = () => {
  const features = [
    'Machine-made Precision',
    'Premium Quality Materials',
    'Custom Size Solutions',
    'Modern Hardware',
    'Soft-close Mechanisms',
    'Water-resistant Finishes',
    'Easy Maintenance',
    'Long-lasting Durability'
  ];

  const services = [
    {
      icon: <FaUtensils className="text-gold-500 text-2xl" />,
      title: 'Modular Kitchens',
      description: 'Contemporary kitchen designs with smart storage solutions and premium finishes'
    },
    {
      icon: <FaTshirt className="text-gold-500 text-2xl" />,
      title: 'Modular Wardrobes',
      description: 'Spacious wardrobe solutions with organized compartments and modern aesthetics'
    },
    {
      icon: <FaCog className="text-gold-500 text-2xl" />,
      title: 'Custom Solutions',
      description: 'Tailored designs to fit your specific space requirements and lifestyle needs'
    }
  ];

  const kitchenStyles = [
    {
      id: 1,
      title: 'L-Shaped Kitchen',
      description: 'Perfect for corner spaces with efficient workflow and ample storage',
      image: dummyImage1,
      features: ['Corner Optimization', 'Efficient Workflow', 'Maximum Storage']
    },
    {
      id: 2,
      title: 'Parallel Kitchen',
      description: 'Ideal for narrow spaces with parallel counters and smart storage',
      image: dummyImage2,
      features: ['Space Efficient', 'Easy Movement', 'Dual Counter Space']
    },
    {
      id: 3,
      title: 'Island Kitchen',
      description: 'Spacious design with central island for cooking and dining',
      image: dummyImage3,
      features: ['Central Island', 'Social Cooking', 'Premium Aesthetics']
    }
  ];

  const wardrobeTypes = [
    {
      id: 1,
      title: 'Walk-in Wardrobe',
      description: 'Luxurious walk-in closet with organized sections for all your belongings',
      features: ['Spacious Design', 'Organized Sections', 'Premium Finishes']
    },
    {
      id: 2,
      title: 'Sliding Door Wardrobe',
      description: 'Space-saving sliding door wardrobes with modern mechanisms',
      features: ['Space Saving', 'Smooth Operation', 'Contemporary Design']
    },
    {
      id: 3,
      title: 'Hinged Door Wardrobe',
      description: 'Classic hinged door wardrobes with customizable interior layouts',
      features: ['Classic Design', 'Customizable Interior', 'Durable Hardware']
    }
  ];

  return (
    <div className="bg-richblack-900 min-h-screen">
      <Navbar />
      <Breadcrumb />

      {/* Hero Section */}
      <div className="relative h-[400px] w-full overflow-hidden">
        <div className="absolute inset-0 bg-black/70 z-10"></div>
        <img
          src={headerImg}
          alt="Modular Kitchen & Wardrobes"
          className="h-full w-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4">
          <h1 className="text-3xl md:text-5xl font-inter font-bold text-white mb-4">
            Modular Kitchen & Wardrobes
          </h1>
          <p className="text-lg text-gray-200 max-w-3xl mb-6">
            Machine-made modular solutions for kitchens and wardrobes with premium quality materials and modern designs
          </p>
          <Link
            to="/contact"
            className="px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
          >
            Get Quote
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        {/* Service Overview */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold text-white mb-6">
              Premium Modular Solutions
            </h2>
            <p className="text-gray-300 mb-6">
              Our modular kitchen and wardrobe solutions combine functionality with aesthetics. 
              Using machine-made precision and premium materials, we create storage solutions 
              that are both beautiful and highly functional.
            </p>
            <p className="text-gray-300 mb-8">
              Every piece is crafted with attention to detail, ensuring durability and style 
              that lasts for years. Our modular approach allows for easy customization to 
              fit your specific space and requirements.
            </p>
            
            {/* Features List */}
            <div className="grid grid-cols-2 gap-3">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <FaCheck className="text-gold-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-6">
            <img
              src={dummyImage1}
              alt="Modular Solutions"
              className="w-full h-64 object-cover rounded-lg"
            />
            <div className="bg-richblack-800 p-6 rounded-lg">
              <h3 className="text-xl font-bold text-white mb-4">Why Choose Modular?</h3>
              <ul className="space-y-3 text-gray-300">
                <li>• Machine-made precision for perfect fit</li>
                <li>• Easy installation and maintenance</li>
                <li>• Customizable to any space</li>
                <li>• Premium hardware and accessories</li>
                <li>• Long-lasting durability</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Our Modular Solutions
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-richblack-800 p-6 rounded-lg text-center hover:bg-richblack-700 transition-all duration-300">
                <div className="mb-4 flex justify-center">
                  {service.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-300 text-sm">
                  {service.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Kitchen Styles */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Kitchen Styles
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {kitchenStyles.map((style) => (
              <div key={style.id} className="bg-richblack-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                <img
                  src={style.image}
                  alt={style.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-3">
                    {style.title}
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    {style.description}
                  </p>
                  <ul className="space-y-2">
                    {style.features.map((feature, index) => (
                      <li key={index} className="text-gray-300 text-sm flex items-center">
                        <FaCheck className="text-gold-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Wardrobe Types */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Wardrobe Solutions
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {wardrobeTypes.map((type) => (
              <div key={type.id} className="bg-richblack-800 p-6 rounded-lg hover:bg-richblack-700 transition-all duration-300">
                <h3 className="text-xl font-bold text-white mb-3">
                  {type.title}
                </h3>
                <p className="text-gray-300 text-sm mb-4">
                  {type.description}
                </p>
                <ul className="space-y-2">
                  {type.features.map((feature, index) => (
                    <li key={index} className="text-gray-300 text-sm flex items-center">
                      <FaCheck className="text-gold-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Process Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Our Process
          </h2>
          <div className="grid md:grid-cols-4 gap-6">
            {[
              { step: '01', title: 'Measurement', desc: 'Precise site measurement and space analysis' },
              { step: '02', title: 'Design', desc: 'Custom design based on your requirements' },
              { step: '03', title: 'Manufacturing', desc: 'Machine-made precision manufacturing' },
              { step: '04', title: 'Installation', desc: 'Professional installation and finishing' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gold-500 text-richblack-900 rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4">
                  {item.step}
                </div>
                <h3 className="text-lg font-bold text-white mb-2">{item.title}</h3>
                <p className="text-gray-300 text-sm">{item.desc}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-richblack-800 to-richblack-900 rounded-lg p-8 md:p-12 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Ready to Upgrade Your Space?</h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Transform your kitchen and bedroom with our premium modular solutions. Get a free consultation today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="inline-flex items-center gap-2 px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
            >
              <FaPhone className="text-sm" />
              Get Free Quote
            </Link>
            <a
              href="tel:9599222158"
              className="inline-flex items-center gap-2 px-8 py-3 bg-transparent border border-gold-500 text-gold-500 rounded-sm font-medium hover:bg-gold-500/10 transition-all duration-300"
            >
              <FaEnvelope className="text-sm" />
              Call: 9599222158
            </a>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default ModularKitchenWardrobes;
