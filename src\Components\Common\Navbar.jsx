import { useState, useEffect } from 'react';
import { NavLink, Link } from 'react-router-dom';
// import videoLogo from '../../assets/Video/dymaic_logo.mp4.mp4';
import logoImg from '../../assets/logo/Engraved_Gold_&_Silver_01[1](1).png';
import {
  FaBars,
  FaTimes,
  FaInstagram,
  FaFacebook,
  FaTwitter,
  FaPhone,
  FaUser, // <-- Add this line
} from 'react-icons/fa';

const Navbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => setMenuOpen(!menuOpen);
  const closeMenu = () => setMenuOpen(false);


  // CSS Classes
  const navLinkClass = "text-white font-inter text-sm tracking-wide font-medium hover:text-orange-400 transition-colors duration-300";
  const activeNavLinkClass = "text-orange-400 font-inter text-sm tracking-wide font-medium";
  const buttonPrimaryClass = "px-6 py-2 bg-gold-500 text-richblack-900 rounded-sm font-semibold hover:bg-gold-400 transition-all duration-300";

  return (
    <header className={`w-full sticky top-0 z-50 transition-all duration-300 ${scrolled ? 'py-2 shadow-lg bg-richblack-900 bg-opacity-95 backdrop-blur-sm' : 'py-4 bg-richblack-900'
      }`}>
      <div className="max-w-7xl mx-auto flex items-center justify-between px-4 lg:px-8">
        {/* Left Side - Logo and Call Button */}
        <div className="flex items-center gap-4">
          <Link to="/" onClick={closeMenu} className="flex items-center">
            <img
              src={logoImg}
              alt="Sparrow Interiors"
              className="w-[120px] h-[60px] object-contain"
            />
          </Link>

          {/* Call Us Button - Left Side */}

        </div>

        {/* Desktop Nav */}
        <nav className="hidden md:flex items-center gap-6 lg:gap-8">
          <NavLink
            to="/"
            className={({ isActive }) => isActive ? activeNavLinkClass : navLinkClass}
          >
            Home
          </NavLink>

          <NavLink
            to="/services"
            className={({ isActive }) => isActive ? activeNavLinkClass : navLinkClass}
          >
            Services
          </NavLink>

          <NavLink
            to="/store"
            className={({ isActive }) => isActive ? activeNavLinkClass : navLinkClass}
          >
            Store
          </NavLink>

          <NavLink
            to="/projects"
            className={({ isActive }) => isActive ? activeNavLinkClass : navLinkClass}
          >
            Projects
          </NavLink>

          <NavLink
            to="/contact"
            className={({ isActive }) => isActive ? activeNavLinkClass : navLinkClass}
          >
            Contact
          </NavLink>
        </nav>

        {/* Right Side Actions */}
        <div className="flex items-center gap-3 lg:gap-4">
          {/* Call Us Button */}
          <a
            href="tel:+919599222158"
            className="hidden md:flex items-center gap-2 text-white hover:text-gold-300 transition-colors duration-200"
          >
            <FaPhone className="text-gold-300" />
            <span className="text-sm font-medium">9599222158</span>
          </a>

          {/* Social Media Icons - Desktop */}
          <div className="hidden md:flex items-center gap-3 border-l border-r border-richblack-700 px-3 py-1">
            <a href="https://instagram.com/sparrowinteriors.in" target="_blank" rel="noopener noreferrer" className="text-white hover:text-gold-300 transition-colors duration-200">
              <FaInstagram />
            </a>
            <a href="https://facebook.com/sparrowinterior" target="_blank" rel="noopener noreferrer" className="text-white hover:text-gold-300 transition-colors duration-200">
              <FaFacebook />
            </a>
            <a href="https://twitter.com/SparrowInter" target="_blank" rel="noopener noreferrer" className="text-white hover:text-gold-300 transition-colors duration-200">
              <FaTwitter />
            </a>
          </div>

          <Link to="/estimate" className="hidden md:block">
            <button className={buttonPrimaryClass}>
              Get Free Estimate
            </button>
          </Link>





          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="md:hidden relative p-2 rounded-full bg-richblack-800 hover:bg-gold-900/30 text-gold-400 text-xl transition-all duration-300"
            aria-label="Toggle menu"
          >
            {menuOpen ? <FaTimes className="transform rotate-90 transition-transform duration-300" /> : <FaBars className="transform transition-transform duration-300" />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {menuOpen && (
        <div className="md:hidden fixed inset-0 z-50 bg-black bg-opacity-50 backdrop-blur-sm">
          <div className="h-full w-[90%] max-w-sm bg-gradient-to-b from-richblack-900 to-richblack-800 overflow-y-auto animate-slide-in-right pb-20">
            <div className="sticky top-0 z-10 flex justify-between items-center p-5 border-b border-richblack-700 bg-richblack-900">
              <Link to="/" onClick={closeMenu} className="flex items-center">
                <img
                  src={logoImg}
                  alt="Sparrow Interiors"
                  className="w-[120px] h-[60px] object-contain"
                />
              </Link>
              <button
                onClick={closeMenu}
                className="text-white text-xl p-2 hover:text-gold-400 transition-colors duration-200"
                aria-label="Close menu"
              >
                <FaTimes />
              </button>
            </div>

            <div className="p-5 space-y-6 pb-20">
              {/* Navigation Links */}
              <div className="space-y-2">
                <NavLink
                  to="/"
                  onClick={closeMenu}
                  className={({ isActive }) => `block py-3 px-4 rounded-sm transition-colors duration-200 ${isActive
                    ? 'bg-gold-500/10 text-gold-400 border-l-2 border-gold-500'
                    : 'text-white hover:bg-richblack-700 hover:text-gold-400 hover:border-l-2 hover:border-gold-500'
                    }`}
                  end
                >
                  Home
                </NavLink>

                <NavLink
                  to="/services"
                  onClick={closeMenu}
                  className={({ isActive }) => `block py-3 px-4 rounded-sm transition-colors duration-200 ${isActive
                      ? 'bg-gold-500/10 text-gold-400 border-l-2 border-gold-500'
                      : 'text-white hover:bg-richblack-700 hover:text-gold-400 hover:border-l-2 hover:border-gold-500'
                    }`}
                >
                  Services
                </NavLink>

                <NavLink
                  to="/store"
                  onClick={closeMenu}
                  className={({ isActive }) => `block py-3 px-4 rounded-sm transition-colors duration-200 ${isActive
                      ? 'bg-gold-500/10 text-gold-400 border-l-2 border-gold-500'
                      : 'text-white hover:bg-richblack-700 hover:text-gold-400 hover:border-l-2 hover:border-gold-500'
                    }`}
                >
                  Store
                </NavLink>

                <NavLink
                  to="/projects"
                  onClick={closeMenu}
                  className={({ isActive }) => `block py-3 px-4 rounded-sm transition-colors duration-200 ${isActive
                      ? 'bg-gold-500/10 text-gold-400 border-l-2 border-gold-500'
                      : 'text-white hover:bg-richblack-700 hover:text-gold-400 hover:border-l-2 hover:border-gold-500'
                    }`}
                >
                  Projects
                </NavLink>



                <NavLink
                  to="/contact"
                  onClick={closeMenu}
                  className={({ isActive }) => `block py-3 px-4 rounded-sm transition-colors duration-200 ${isActive
                      ? 'bg-gold-500/10 text-gold-400 border-l-2 border-gold-500'
                      : 'text-white hover:bg-richblack-700 hover:text-gold-400 hover:border-l-2 hover:border-gold-500'
                    }`}
                >
                  Contact
                </NavLink>
              </div>

              {/* Call to Action */}
              <div className="mt-8 space-y-4">
                <Link to="/estimate" onClick={closeMenu}>
                  <button className="w-full bg-gold-500 text-richblack-900 font-medium py-3 px-4 rounded-sm hover:bg-gold-400 transition-colors duration-300 flex items-center justify-center gap-2">
                    <span>Get Free Estimate</span>
                  </button>
                </Link>

                <a
                  href="tel:+91 9599222158"
                  className="flex items-center justify-center gap-2 py-3 px-4 rounded-sm bg-richblack-700 text-white hover:text-gold-400 transition-colors duration-200"
                >
                  <FaPhone className="transform rotate-180" /> <span>9599222158</span>
                </a>
              </div>

              {/* Auth Buttons */}
              <div className="flex gap-3 mt-6">
                <Link to="/login" onClick={closeMenu} className="flex-1">
                  <button className="w-full flex items-center justify-center gap-2 text-white border border-richblack-700 font-medium py-3 px-4 rounded-sm hover:border-gold-500 hover:bg-richblack-700 transition-colors duration-300">
                    <FaUser className="text-gold-500" /> Login
                  </button>
                </Link>
                <Link to="/signup" onClick={closeMenu} className="flex-1">
                  <button className="w-full flex items-center justify-center gap-2 text-white border border-richblack-700 font-medium py-3 px-4 rounded-sm hover:border-gold-500 hover:bg-richblack-700 transition-colors duration-300">
                    <FaUser className="text-gold-500" /> Sign Up
                  </button>
                </Link>
              </div>



              {/* Social Links */}
              <div className="flex justify-center gap-6 py-8 mt-8 border-t border-richblack-700 relative">
                <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 px-4 bg-richblack-800">
                  <span className="text-gold-400 text-xs font-medium">Connect With Us</span>
                </div>
                <a href="https://instagram.com/sparrowinteriors.in" target="_blank" rel="noopener noreferrer" className="bg-richblack-700 p-3 rounded-full text-white hover:text-richblack-900 hover:bg-gold-500 transition-all duration-300">
                  <FaInstagram />
                </a>
                <a href="https://facebook.com/sparrowinterior" target="_blank" rel="noopener noreferrer" className="bg-richblack-700 p-3 rounded-full text-white hover:text-richblack-900 hover:bg-gold-500 transition-all duration-300">
                  <FaFacebook />
                </a>
                <a href="https://twitter.com/SparrowInter" target="_blank" rel="noopener noreferrer" className="bg-richblack-700 p-3 rounded-full text-white hover:text-richblack-900 hover:bg-gold-500 transition-all duration-300">
                  <FaTwitter />
                </a>
              </div>
            </div>
          </div>

          {/* Backdrop click to close */}
          <div
            className="h-full w-[15%] ml-auto"
            onClick={closeMenu}
          ></div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
