import { useState, useEffect } from 'react';
import { FaTimes, FaHome, FaCouch, FaUtensils, FaDoorOpen } from 'react-icons/fa';

const DelayedModalForm = ({ theme = 'dark', delayTime = 4000 }) => {
  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: 'full-home',
    message: '',
  });
  const [errors, setErrors] = useState({});
  const [step, setStep] = useState(1);
  const [submitted, setSubmitted] = useState(false);

  // Show modal after delay
  useEffect(() => {
    // Always show the modal after the specified delay
    // This ensures it appears every time the component is mounted
    const timer = setTimeout(() => {
      setShowModal(true);

      // Optional: Uncomment the following line if you want the modal to appear only once per session
      // sessionStorage.setItem('modalShown', 'true');
    }, delayTime);

    return () => clearTimeout(timer);
  }, [delayTime]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: null });
    }
  };

  const validateStep = (currentStep) => {
    const newErrors = {};

    if (currentStep === 1) {
      if (!formData.name.trim()) newErrors.name = "Name is required";
      if (!formData.email.trim()) {
        newErrors.email = "Email is required";
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = "Invalid email format";
      }
      if (!formData.phone.trim()) {
        newErrors.phone = "Phone number is required";
      }
    }

    if (currentStep === 2) {
      if (!formData.service) newErrors.service = "Please select a service";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    setStep(step - 1);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateStep(step)) {
      console.log("Form submitted:", formData);
      // Here you would typically send the data to your backend
      setSubmitted(true);

      // Close modal after showing success message
      setTimeout(() => {
        setShowModal(false);
        // Reset form for next time
        setFormData({
          name: '',
          email: '',
          phone: '',
          service: 'full-home',
          message: '',
        });
        setStep(1);
        setSubmitted(false);
      }, 3000);
    }
  };

  // If modal is not shown, don't render anything
  if (!showModal) return null;

  // Service options with icons
  const serviceOptions = [
    { id: 'full-home', name: 'Full Home Interior', icon: <FaHome className="text-3xl" /> },
    { id: 'furniture', name: 'Custom Furniture', icon: <FaCouch className="text-3xl" /> },
    { id: 'kitchen', name: 'Kitchen Design', icon: <FaUtensils className="text-3xl" /> },
    { id: 'wardrobe', name: 'Wardrobe Solutions', icon: <FaDoorOpen className="text-3xl" /> },
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop with fade-in animation */}
      <div
        className="absolute inset-0 bg-black bg-opacity-70 backdrop-blur-sm animate-fade-in"
        onClick={() => setShowModal(false)}
      ></div>

      {/* Modal with bounce-in animation and attention-grabbing pulse */}
      <div className={`relative w-11/12 max-w-md p-6 rounded-lg shadow-2xl animate-bounce-in animate-pulse-attention ${
        theme === 'light' ? 'bg-white text-primary-900' : 'bg-richblack-800 text-white'
      }`}>
        {/* Close button */}
        <button
          onClick={() => setShowModal(false)}
          className={`absolute top-3 right-3 text-xl hover:text-gold-300 transition-colors duration-200 ${
            theme === 'light' ? 'text-primary-900' : 'text-white'
          }`}
        >
          <FaTimes />
        </button>

        {submitted ? (
          <div className="text-center py-8">
            {/* Gold accent line */}
            <div className="w-16 h-1 bg-gradient-to-r from-gold-700 via-gold-300 to-gold-700 mx-auto mb-4"></div>

            <div className={`text-6xl mb-4 ${theme === 'light' ? 'text-gold-500' : 'text-gold-300'} animate-bounce-in`}>✓</div>
            <h2 className="text-3xl font-inter font-semibold mb-3">Thank You!</h2>
            <p className={`${theme === 'light' ? 'text-primary-700' : 'text-gray-300'} mb-4`}>
              We've received your request and will contact you shortly.
            </p>

            {/* Gold accent line */}
            <div className="w-12 h-0.5 bg-gradient-to-r from-gold-700 via-gold-300 to-gold-700 mx-auto"></div>
          </div>
        ) : (
          <>
            {/* Gold accent line */}
            <div className="w-20 h-1 bg-gradient-to-r from-gold-700 via-gold-300 to-gold-700 mx-auto mb-4"></div>

            <h2 className={`text-3xl font-inter font-semibold mb-3 text-center ${
              theme === 'light' ? 'text-primary-900' : 'text-white'
            }`}>
              <span className={theme === 'light' ? 'text-gold-600' : 'text-gold-300'}>Transform</span> Your Space
            </h2>
            <p className={`text-center mb-6 ${
              theme === 'light' ? 'text-primary-700' : 'text-gray-300'
            }`}>
              Get a <span className="font-semibold">FREE</span> consultation with our design experts
            </p>

            {/* Gold accent line */}
            <div className="w-16 h-0.5 bg-gradient-to-r from-gold-700 via-gold-300 to-gold-700 mx-auto mb-6"></div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {step === 1 && (
                <>
                  <div>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Your Name"
                      className={`w-full p-3 rounded-sm focus:outline-none focus:ring-2 focus:ring-gold-400 ${
                        theme === 'light'
                          ? 'bg-gray-100 text-primary-900 border border-gray-200'
                          : 'bg-richblack-700 text-white border border-richblack-600'
                      }`}
                    />
                    {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                  </div>

                  <div>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="Your Email"
                      className={`w-full p-3 rounded-sm focus:outline-none focus:ring-2 focus:ring-gold-400 ${
                        theme === 'light'
                          ? 'bg-gray-100 text-primary-900 border border-gray-200'
                          : 'bg-richblack-700 text-white border border-richblack-600'
                      }`}
                    />
                    {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                  </div>

                  <div>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="Your Phone"
                      className={`w-full p-3 rounded-sm focus:outline-none focus:ring-2 focus:ring-gold-400 ${
                        theme === 'light'
                          ? 'bg-gray-100 text-primary-900 border border-gray-200'
                          : 'bg-richblack-700 text-white border border-richblack-600'
                      }`}
                    />
                    {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
                  </div>

                  <button
                    type="button"
                    onClick={nextStep}
                    className={`w-full py-3 rounded-sm uppercase tracking-wider text-sm font-medium ${
                      theme === 'light'
                        ? 'bg-gold-400 text-white hover:bg-gold-500'
                        : 'bg-gold-300 text-richblack-900 hover:bg-gold-400'
                    } transition-colors duration-300 shadow-lg flex items-center justify-center gap-2`}
                  >
                    Next Step <span className="text-lg">→</span>
                  </button>
                </>
              )}

              {step === 2 && (
                <>
                  <p className={`font-medium mb-3 ${
                    theme === 'light' ? 'text-primary-800' : 'text-gray-200'
                  }`}>
                    What service are you interested in?
                  </p>

                  <div className="grid grid-cols-2 gap-4 mb-6">
                    {serviceOptions.map((service) => (
                      <div
                        key={service.id}
                        onClick={() => setFormData({ ...formData, service: service.id })}
                        className={`flex flex-col items-center p-4 rounded-sm cursor-pointer transition-all duration-200 transform hover:scale-105 ${
                          formData.service === service.id
                            ? theme === 'light'
                              ? 'bg-gold-100 border-2 border-gold-400 shadow-lg'
                              : 'bg-richblack-700 border-2 border-gold-300 shadow-lg'
                            : theme === 'light'
                              ? 'bg-gray-100 border border-gray-200 hover:bg-gold-50'
                              : 'bg-richblack-700 border border-richblack-600 hover:bg-richblack-600'
                        }`}
                      >
                        <div className={`mb-3 ${
                          formData.service === service.id
                            ? theme === 'light'
                              ? 'text-gold-600'
                              : 'text-gold-300'
                            : theme === 'light'
                              ? 'text-primary-800'
                              : 'text-white'
                        }`}>
                          {service.icon}
                        </div>
                        <span className={`text-sm font-medium text-center ${
                          formData.service === service.id
                            ? theme === 'light'
                              ? 'text-gold-600'
                              : 'text-gold-300'
                            : ''
                        }`}>
                          {service.name}
                        </span>
                      </div>
                    ))}
                  </div>

                  <div>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      placeholder="Tell us about your project (optional)"
                      rows="3"
                      className={`w-full p-3 rounded-sm focus:outline-none focus:ring-2 focus:ring-gold-400 ${
                        theme === 'light'
                          ? 'bg-gray-100 text-primary-900 border border-gray-200'
                          : 'bg-richblack-700 text-white border border-richblack-600'
                      }`}
                    />
                  </div>

                  <div className="flex gap-3">
                    <button
                      type="button"
                      onClick={prevStep}
                      className={`flex-1 py-3 rounded-sm uppercase tracking-wider text-sm font-medium ${
                        theme === 'light'
                          ? 'bg-gray-200 text-primary-900 hover:bg-gray-300'
                          : 'bg-richblack-600 text-white hover:bg-richblack-500'
                      } transition-colors duration-300`}
                    >
                      Back
                    </button>

                    <button
                      type="submit"
                      className={`flex-1 py-3 rounded-sm uppercase tracking-wider text-sm font-medium ${
                        theme === 'light'
                          ? 'bg-gold-400 text-white hover:bg-gold-500'
                          : 'bg-gold-300 text-richblack-900 hover:bg-gold-400'
                      } transition-colors duration-300 shadow-lg flex items-center justify-center gap-2`}
                    >
                      <span className="relative">
                        Submit
                        <span className="absolute -top-1 -right-2 flex h-3 w-3">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                          <span className="relative inline-flex rounded-full h-3 w-3 bg-white"></span>
                        </span>
                      </span>
                    </button>
                  </div>
                </>
              )}
            </form>
          </>
        )}
      </div>
    </div>
  );
};

export default DelayedModalForm;
