
import { Link } from 'react-router-dom';
import Navbar from '../../Components/Common/Navbar';
import { Footer } from '../../Components/Common/Footer';
import Breadcrumb from '../../Components/Common/Breadcrumb';
import { FaCheck, FaPhone, FaEnvelope, FaBuilding, FaHome, FaCog } from 'react-icons/fa';

// Import images
import headerImg from '../../assets/Images/Header_Image/150295.jpg';
import dummyImage1 from '../../assets/Images/About1.webp';
import dummyImage2 from '../../assets/Images/About2.webp';
import dummyImage3 from '../../assets/Images/About3.webp';

const CommercialResidential = () => {
  const features = [
    'Space Planning & Layout',
    'Interior Design Concepts',
    '3D Visualization',
    'Material Selection',
    'Lighting Design',
    'Color Schemes',
    'Furniture Planning',
    'Project Management'
  ];

  const services = [
    {
      icon: <FaBuilding className="text-gold-500 text-2xl" />,
      title: 'Commercial Design',
      description: 'Professional design solutions for offices, retail spaces, restaurants, and commercial establishments'
    },
    {
      icon: <FaHome className="text-gold-500 text-2xl" />,
      title: 'Residential Design',
      description: 'Complete home interior design services for apartments, villas, and independent houses'
    },
    {
      icon: <FaCog className="text-gold-500 text-2xl" />,
      title: 'Design Consultation',
      description: 'Expert consultation services for design planning, material selection, and project execution'
    }
  ];

  const projects = [
    {
      id: 1,
      title: 'Modern Office Complex',
      description: 'Complete interior design for a 10,000 sq.ft corporate office with modern amenities',
      image: dummyImage1,
      area: '10,000 sq.ft'
    },
    {
      id: 2,
      title: 'Luxury Residential Villa',
      description: 'Comprehensive interior design for a 4-bedroom luxury villa with premium finishes',
      image: dummyImage2,
      area: '4,500 sq.ft'
    },
    {
      id: 3,
      title: 'Retail Showroom',
      description: 'Contemporary design for a premium retail showroom with customer-focused layout',
      image: dummyImage3,
      area: '3,200 sq.ft'
    }
  ];

  return (
    <div className="bg-richblack-900 min-h-screen">
      <Navbar />
      <Breadcrumb />

      {/* Hero Section */}
      <div className="relative h-[400px] w-full overflow-hidden">
        <div className="absolute inset-0 bg-black/70 z-10"></div>
        <img
          src={headerImg}
          alt="Commercial & Residential Designing"
          className="h-full w-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4">
          <h1 className="text-3xl md:text-5xl font-inter font-bold text-white mb-4">
            Commercial & Residential Designing
          </h1>
          <p className="text-lg text-gray-200 max-w-3xl mb-6">
            Professional design services for both commercial spaces and residential properties with expert planning and execution
          </p>
          <Link
            to="/contact"
            className="px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
          >
            Get Consultation
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        {/* Service Overview */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold text-white mb-6">
              Expert Design Solutions
            </h2>
            <p className="text-gray-300 mb-6">
              Our team of experienced designers specializes in creating functional and aesthetically 
              pleasing spaces for both commercial and residential projects. We understand the unique 
              requirements of each space type and deliver customized solutions.
            </p>
            <p className="text-gray-300 mb-8">
              From concept development to final execution, we ensure every detail is carefully 
              planned and executed to perfection, creating spaces that inspire and function beautifully.
            </p>
            
            {/* Features List */}
            <div className="grid grid-cols-2 gap-3">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <FaCheck className="text-gold-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-6">
            <img
              src={dummyImage1}
              alt="Design Process"
              className="w-full h-64 object-cover rounded-lg"
            />
            <div className="bg-richblack-800 p-6 rounded-lg">
              <h3 className="text-xl font-bold text-white mb-4">Why Choose Us</h3>
              <ul className="space-y-3 text-gray-300">
                <li>• 10+ years of design experience</li>
                <li>• Expert team of certified designers</li>
                <li>• Customized design solutions</li>
                <li>• Timely project delivery</li>
                <li>• Post-completion support</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Our Design Services
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-richblack-800 p-6 rounded-lg text-center hover:bg-richblack-700 transition-all duration-300">
                <div className="mb-4 flex justify-center">
                  {service.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-300 text-sm">
                  {service.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Project Gallery */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Featured Projects
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {projects.map((project) => (
              <div key={project.id} className="bg-richblack-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-xs px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full">
                      {project.area}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-3">
                    {project.title}
                  </h3>
                  <p className="text-gray-300 text-sm">
                    {project.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Process Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Our Design Process
          </h2>
          <div className="grid md:grid-cols-4 gap-6">
            {[
              { step: '01', title: 'Consultation', desc: 'Understanding your requirements and vision' },
              { step: '02', title: 'Concept', desc: 'Creating initial design concepts and layouts' },
              { step: '03', title: 'Design', desc: 'Detailed design development with 3D visualization' },
              { step: '04', title: 'Execution', desc: 'Project implementation with quality control' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gold-500 text-richblack-900 rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4">
                  {item.step}
                </div>
                <h3 className="text-lg font-bold text-white mb-2">{item.title}</h3>
                <p className="text-gray-300 text-sm">{item.desc}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-richblack-800 to-richblack-900 rounded-lg p-8 md:p-12 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Ready to Transform Your Space?</h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Contact us today for a free consultation and let our design experts create the perfect space for you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="inline-flex items-center gap-2 px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
            >
              <FaPhone className="text-sm" />
              Call Now
            </Link>
            <Link
              to="/appointment"
              className="inline-flex items-center gap-2 px-8 py-3 bg-transparent border border-gold-500 text-gold-500 rounded-sm font-medium hover:bg-gold-500/10 transition-all duration-300"
            >
              <FaEnvelope className="text-sm" />
              Schedule Consultation
            </Link>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default CommercialResidential;
