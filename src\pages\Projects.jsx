import { useState } from 'react';
import { Link } from 'react-router-dom';
import Navbar from '../Components/Common/Navbar';
import { Footer } from '../Components/Common/Footer';
import Breadcrumb from '../Components/Common/Breadcrumb';

// Import header image
import headerImg from '../assets/Images/Header_Image/44477.jpg';

// Import project images (using available images)
import project1 from '../assets/Images/About1.webp';
import project2 from '../assets/Images/About2.webp';
import project3 from '../assets/Images/About3.webp';
import project4 from '../assets/Images/Story.png';
import project5 from '../assets/Images/furniture_selection.jpeg';
import project6 from '../assets/Images/custom_interior_design.jpeg';
import project7 from '../assets/Images/space_planning.jpeg';
import project8 from '../assets/Images/home_renovation.jpeg';

const Projects = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const projects = [
    {
      id: 1,
      title: 'Modern Luxury Villa',
      category: 'residential',
      location: 'Delhi NCR',
      area: '4,500 sq.ft',
      year: '2024',
      image: project1,
      slug: 'modern-luxury-villa',
      description: 'A contemporary luxury villa featuring modern design elements with traditional Indian touches.',
      client: 'Private Residence',
      challenge: 'The client wanted a modern villa that would reflect contemporary design while maintaining a connection to traditional Indian architecture. The challenge was to create a space that felt both luxurious and homely, incorporating sustainable materials and energy-efficient systems.',
      solution: 'We designed a stunning villa that seamlessly blends modern aesthetics with traditional Indian elements. The use of natural materials, open spaces, and strategic lighting creates a harmonious living environment that celebrates both comfort and style.',
      features: [
        'Open-plan living spaces',
        'Traditional Indian architectural elements',
        'Sustainable materials throughout',
        'Energy-efficient lighting systems',
        'Custom-designed furniture',
        'Landscaped gardens',
        'Smart home automation',
        'Natural ventilation systems'
      ],
      images: [project1, project2, project3, project4],
      relatedProjects: [
        { title: 'Corporate Office Design', image: project2, slug: 'corporate-office-design' },
        { title: 'Boutique Hotel Interior', image: project3, slug: 'boutique-hotel-interior' },
        { title: 'Premium Apartment', image: project4, slug: 'premium-apartment' }
      ]
    },
    {
      id: 2,
      title: 'Corporate Office Design',
      category: 'commercial',
      location: 'Gurgaon',
      area: '8,000 sq.ft',
      year: '2024',
      image: project2,
      slug: 'corporate-office-design',
      description: 'Innovative office space design promoting productivity and employee well-being.',
      client: 'Tech Corporation',
      challenge: 'The client needed a modern office space that would foster collaboration while providing quiet zones for focused work. The challenge was to create an environment that would boost productivity and employee satisfaction within a limited budget.',
      solution: 'We created a dynamic workspace with flexible zones, incorporating biophilic design elements and ergonomic furniture. The design promotes both collaboration and individual focus, with natural lighting and sustainable materials throughout.',
      features: [
        'Flexible workspace zones',
        'Biophilic design elements',
        'Ergonomic furniture',
        'Natural lighting optimization',
        'Collaborative meeting spaces',
        'Quiet focus areas',
        'Sustainable materials',
        'Modern technology integration'
      ],
      images: [project2, project1, project3, project4],
      relatedProjects: [
        { title: 'Modern Luxury Villa', image: project1, slug: 'modern-luxury-villa' },
        { title: 'Boutique Hotel Interior', image: project3, slug: 'boutique-hotel-interior' },
        { title: 'Contemporary Restaurant', image: project5, slug: 'contemporary-restaurant' }
      ]
    },
    {
      id: 3,
      title: 'Boutique Hotel Interior',
      category: 'hospitality',
      location: 'Mumbai',
      area: '12,000 sq.ft',
      year: '2023',
      image: project3,
      slug: 'boutique-hotel-interior',
      description: 'Elegant boutique hotel interior combining luxury with local cultural elements.',
      client: 'Hospitality Group'
    },
    {
      id: 4,
      title: 'Premium Apartment',
      category: 'residential',
      location: 'Bangalore',
      area: '2,800 sq.ft',
      year: '2023',
      image: project4,
      slug: 'premium-apartment',
      description: 'Sophisticated apartment design maximizing space and natural light.',
      client: 'Private Client'
    },
    {
      id: 5,
      title: 'Contemporary Restaurant',
      category: 'commercial',
      location: 'Delhi',
      area: '3,500 sq.ft',
      year: '2024',
      image: project5,
      slug: 'contemporary-restaurant',
      description: 'Contemporary restaurant design creating an immersive dining experience.',
      client: 'Restaurant Chain'
    },
    {
      id: 6,
      title: 'Luxury Penthouse',
      category: 'residential',
      location: 'Mumbai',
      area: '6,200 sq.ft',
      year: '2023',
      image: project6,
      slug: 'luxury-penthouse',
      description: 'Opulent penthouse design with panoramic city views and premium finishes.',
      client: 'High-Net-Worth Individual'
    },
    {
      id: 7,
      title: 'Modern Office Complex',
      category: 'commercial',
      location: 'Noida',
      area: '15,000 sq.ft',
      year: '2024',
      image: project7,
      slug: 'modern-office-complex',
      description: 'Large-scale office complex with innovative workspace solutions.',
      client: 'Corporate Group'
    },
    {
      id: 8,
      title: 'Heritage Home Renovation',
      category: 'residential',
      location: 'Jaipur',
      area: '5,800 sq.ft',
      year: '2023',
      image: project8,
      slug: 'heritage-home-renovation',
      description: 'Careful restoration of heritage home with modern amenities.',
      client: 'Heritage Property Owner'
    }
  ];

  const categories = [
    { id: 'all', name: 'All Projects' },
    { id: 'residential', name: 'Residential' },
    { id: 'commercial', name: 'Commercial' },
    { id: 'hospitality', name: 'Hospitality' }
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <div className="min-h-screen">
      <Navbar />
      <Breadcrumb />

      {/* Hero Section */}
      <div className="relative h-[50vh] md:h-[60vh] lg:h-[70vh] w-full overflow-hidden">
        <div className="absolute inset-0 bg-black/70 z-10"></div>
        <img
          src={headerImg}
          alt="Our Portfolio"
          className="h-full w-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4">
          <h1 className="text-2xl md:text-4xl lg:text-5xl font-inter font-bold text-white mb-4">
            Our Portfolio
          </h1>
          <p className="text-sm md:text-lg text-gray-200 max-w-3xl mb-6 px-4">
            Discover our exceptional interior design projects that transform spaces into extraordinary experiences
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        {/* Filter Tabs */}
        <div className="flex flex-wrap justify-center gap-4 mb-16 py-4">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveFilter(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeFilter === category.id
                  ? 'bg-gold-500 text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gold-100 hover:text-gold-600'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Projects Grid - Responsive Masonry Style */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {filteredProjects.map((project, index) => (
            <Link
              key={project.id}
              to={`/projects/${project.slug}`}
              className="group block"
            >
              <div className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                <div className="relative overflow-hidden">
                  <img
                    src={project.image}
                    alt={project.title}
                    className={`w-full object-cover transform transition-transform duration-700 group-hover:scale-110 ${
                      index % 3 === 0 ? 'h-64 md:h-80' : index % 3 === 1 ? 'h-56 md:h-64' : 'h-60 md:h-72'
                    }`}
                  />

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div className="absolute bottom-4 md:bottom-6 left-4 md:left-6 right-4 md:right-6">
                      <div className="text-white">
                        <h3 className="text-lg md:text-xl font-bold mb-1 md:mb-2">{project.title}</h3>
                        <p className="text-xs md:text-sm opacity-90 mb-2 md:mb-3">{project.client}</p>
                        <div className="flex items-center justify-between text-xs">
                          <span>{project.location}</span>
                          <span>{project.year}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Category Badge */}
                  <div className="absolute top-3 md:top-4 left-3 md:left-4">
                    <span className=" text-richblack-900 text-xs font-semibold px-2 md:px-3 py-1 rounded-full uppercase tracking-wide">
                      {project.category}
                    </span>
                  </div>
                </div>

                {/* Project Info - Always Visible */}
                <div className="p-4 md:p-6 ">
                  <h3 className="text-lg md:text-xl font-bold text-richblack-900 mb-2 group-hover:text-gold-600 transition-colors duration-300">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {project.description}
                  </p>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className="font-medium">{project.location}</span>
                    <span className="hidden sm:inline">{project.area}</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-richblack-800 to-richblack-900 rounded-lg p-8 md:p-12">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Start Your Project?</h2>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              Let us transform your space into something extraordinary. Contact us for a consultation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/contact"
                className="px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
              >
                Start Your Project
              </Link>
              <a
                href="tel:9599222158"
                className="px-8 py-3 bg-transparent border border-gold-500 text-gold-500 rounded-sm font-medium hover:bg-gold-500/10 transition-all duration-300"
              >
                Call: 9599222158
              </a>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Projects;
