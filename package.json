{"name": "sparrow-interior", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "vercel-build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@ramonak/react-progress-bar": "^5.3.0", "@reduxjs/toolkit": "^2.7.0", "@rollup/plugin-terser": "^0.4.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "ajv": "^8.17.1", "axios": "^1.8.4", "chart.js": "^4.4.8", "copy-to-clipboard": "^3.3.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "framer-motion": "^12.6.3", "init": "^0.1.2", "install": "^0.13.0", "lucide-react": "^0.501.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-otp-input": "^3.1.1", "react-redux": "^9.2.0", "react-router-dom": "^7.5.0", "react-scripts": "^5.0.1", "react-slick": "^0.30.3", "react-star-ratings": "^2.3.0", "react-stars": "^2.2.5", "react-super-responsive-table": "^6.0.2", "react-type-animation": "^3.2.0", "redux-toolkit": "^1.1.2", "slick-carousel": "^1.8.1", "swiper": "^11.2.6", "video-react": "^0.16.0", "web-vitals": "^4.2.4"}, "devDependencies": {"@eslint/js": "^8.56.0", "@tailwindcss/postcss": "^4.1.2", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^13.24.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^5.1.4"}}