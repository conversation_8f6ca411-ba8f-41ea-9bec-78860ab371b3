import { Route, Routes } from "react-router-dom"
import RoomSelection from "./RoomSelection";
import { Bhk } from "./Bhk";
import Package from "./Package";
import { GetEstimate } from "./getEstimate";
const FullHomeInteriorRoute = () => {

    return (
        <Routes>
            <Route path='/bhkselection' element={<Bhk/>} />
            <Route path='/bhkselection/roomselection' element={<RoomSelection/>} />
            <Route path='/bhkselection/roomselection/package' element={<Package/>} />
            <Route path='/bhkselection/roomselection/package/getestimate' element={<GetEstimate/>} />
        
        </Routes>
    )
}


export default FullHomeInteriorRoute;