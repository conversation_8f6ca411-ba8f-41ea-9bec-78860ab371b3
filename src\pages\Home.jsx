
import { Suspense, lazy } from 'react';
import LoadingSpinner from '../Components/Common/LoadingSpinner';
import Navbar from '../Components/Common/Navbar';
import { Footer } from '../Components/Common/Footer';

// Lazy load components for better performance
const Hero = lazy(() => import('../Components/HomePage/Hero').then(module => ({ default: module.Hero })));
const ServiceSection = lazy(() => import('../Components/HomePage/ServiceSection').then(module => ({ default: module.ServiceSection })));
const Testimonail = lazy(() => import('../Components/HomePage/Testimonail').then(module => ({ default: module.Testimonail })));
const GetEstimated = lazy(() => import('../Components/HomePage/GetEstimated').then(module => ({ default: module.GetEstimated })));
const WhyChooseUs = lazy(() => import('../Components/HomePage/WhyChooseUs').then(module => ({ default: module.WhyChooseUs })));
const QuoteForm = lazy(() => import('../Components/Common/QuoteForm'));
const OurStoreSection = lazy(() => import('../Components/HomePage/Store'));

export const Home = () => {
    return (
        <div className='bg-richblack-900 min-h-screen'>
            <Navbar/>
            <Suspense fallback={<LoadingSpinner />}>
                <Hero />
                <ServiceSection />
                <GetEstimated />
                <OurStoreSection />
                <WhyChooseUs />
                <Testimonail />
                <QuoteForm />
            </Suspense>
            <Footer />
        </div>
    )
}
