import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const wardrobeMaterials = [
    {
        type: 'Standard - Laminate',
        img: 'https://d3gq2merok8n5r.cloudfront.net/stage2-1623054096-6N9Ti/wardrobe-price-calculator-1672758178-cTiiD/finish-type-1672761015-wBNh1/desktop-1672761020-UrtdK/laminate-d-1672761040-gX5Kj.jpg',
        price: '₹₹',
        tip: "Looking for a seamless finish that sits well with every interior? This one's for you.",
    },
    {
        type: 'Premium - Membrane',
        img: 'https://d3gq2merok8n5r.cloudfront.net/stage2-1623054096-6N9Ti/wardrobe-price-calculator-1672758178-cTiiD/finish-type-1672761015-wBNh1/desktop-1672761020-UrtdK/laminate-d-1672761040-gX5Kj.jpg',
        price: '₹₹₹',
        tip: "A sleek and rich finish perfect for luxury interiors. Great for long-lasting quality.",
    },
    {
        type: 'Premium - Membrane',
        img: 'https://d3gq2merok8n5r.cloudfront.net/stage2-1623054096-6N9Ti/wardrobe-price-calculator-1672758178-cTiiD/finish-type-1672761015-wBNh1/desktop-1672761020-UrtdK/laminate-d-1672761040-gX5Kj.jpg',
        price: '₹₹₹',
        tip: "A sleek and rich finish perfect for luxury interiors. Great for long-lasting quality.",
    }, ,
    {
        type: 'Premium - Membrane',
        img: 'https://d3gq2merok8n5r.cloudfront.net/stage2-1623054096-6N9Ti/wardrobe-price-calculator-1672758178-cTiiD/finish-type-1672761015-wBNh1/desktop-1672761020-UrtdK/laminate-d-1672761040-gX5Kj.jpg',
        price: '₹₹₹',
        tip: "A sleek and rich finish perfect for luxury interiors. Great for long-lasting quality.",
    },
];


const MaterialTypesSamples = () => {
    const [selected, setSelected] = useState('');

    return (
        <div className="min-h-screen bg-[#0B0B0F] px-4 py-12 flex justify-center items-center">
            <div className="w-full max-w-5xl bg-[#121212] rounded-3xl shadow-2xl p-8">
                <h2 className="text-4xl font-bold text-center text-white mb-10 tracking-wider">Choose Your Finish</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {wardrobeMaterials.map((material, index) => (
                        <label
                            key={index}
                            className={`group relative border rounded-2xl transition-all duration-300 p-5 backdrop-blur-md bg-[#1c1c1e]/50 hover:shadow-xl hover:scale-[1.02] cursor-pointer ${selected === material.type
                                ? 'border-yellow-300 shadow-yellow-300/30'
                                : 'border-gray-700'
                                }`}
                        >
                            <div className="flex items-center mb-4">
                                <input
                                    type="radio"
                                    name="material"
                                    className="mr-3 accent-yellow-300 scale-125"
                                    value={material.type}
                                    checked={selected === material.type}
                                    onChange={() => setSelected(material.type)}
                                />
                                <span className="text-xl font-medium text-white">{material.type}</span>
                            </div>

                            <img
                                src={material.img}
                                alt={material.type}
                                className="rounded-xl w-full h-52 object-cover border border-[#2a2a2a] mb-4"
                            />

                            <p className="text-gray-400 mb-1">
                                <span className="text-gray-300 font-semibold">Price</span> :{' '}
                                <span className="text-yellow-300">{material.price}</span>
                            </p>
                            <p className="text-sm text-gray-400">
                                <span className="font-semibold text-white">Pro Tip</span> : {material.tip}
                            </p>
                        </label>
                    ))}
                </div>

                <div className="mt-12 flex justify-between items-center border-t border-gray-700 pt-6">
                    <button className="text-yellow-300 font-semibold hover:underline">BACK</button>
                    <Link to='accessories'>
                        <button
                            className={`px-8 py-3 rounded-full text-white font-bold transition-all tracking-wide ${selected
                                ? 'bg-yellow-300 yellow-300 shadow-md shadow-yellow-300/20'
                                : 'bg-gray-600 cursor-not-allowed'
                                }`}
                            disabled={!selected}
                        >
                            NEXT
                        </button>
                    </Link>
                </div>
            </div>
        </div>
    );
};

export default MaterialTypesSamples;
