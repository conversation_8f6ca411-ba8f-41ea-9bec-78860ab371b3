
import { useForm } from 'react-hook-form'

export const Form = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const submitContactForm = (data) => {
    console.log(data)
  }

  const loading = false

  return (
    <div className="flex flex-col items-center justify-center mt-6 min-h-screen px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-2xl mx-auto">
        <h1 className="text-center text-2xl sm:text-3xl md:text-4xl font-semibold text-white mb-4">
          Get in Touch
        </h1>
        <p className="text-center text-richblack-300 text-sm sm:text-base mb-8">
          We&apos;d love to hear from you, Please fill out this form.
        </p>

        <form className="flex flex-col gap-6 sm:gap-8" onSubmit={handleSubmit(submitContactForm)}>
          {/* Name Fields */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div className="flex flex-col gap-2">
              <label className="text-left text-richblack-5 mb-1 font-inter text-sm sm:text-base">
                First Name *
              </label>
              <input
                type="text"
                name="firstname"
                id="firstname"
                placeholder="Enter first name"
                className="form-input-responsive"
                {...register("firstname", { required: true })}
              />
              {errors.firstname && (
                <span className="text-xs sm:text-sm text-yellow-100 mt-1">
                  Please enter your name.
                </span>
              )}
            </div>
            
            <div className="flex flex-col gap-2">
              <label className="text-left text-richblack-5 mb-1 font-inter text-sm sm:text-base">
                Last Name
              </label>
              <input
                type="text"
                name="lastname"
                id="lastname"
                placeholder="Enter last name"
                className="form-input-responsive"
                {...register("lastname")}
              />
            </div>
          </div>

          {/* Email Field */}
          <div className="flex flex-col gap-2">
            <label className="text-left text-richblack-5 mb-1 font-inter text-sm sm:text-base">
              Email Address *
            </label>
            <input
              type="email"
              name="email"
              id="email"
              placeholder="Enter email address"
              className="form-input-responsive"
              {...register("email", { required: true })}
            />
            {errors.email && (
              <span className="text-xs sm:text-sm text-yellow-100 mt-1">
                Please enter your Email address.
              </span>
            )}
          </div>

          {/* Phone Field */}
          <div className="flex flex-col gap-2">
            <label className="text-left text-richblack-5 mb-1 font-inter text-sm sm:text-base">
              Phone Number *
            </label>
            <input
              type="tel"
              name="phonenumber"
              id="phonenumber"
              placeholder="12345 67890"
              className="form-input-responsive"
              {...register("phoneNo", {
                required: {
                  value: true,
                  message: "Please enter your Phone Number.",
                },
                maxLength: { value: 12, message: "Invalid Phone Number" },
                minLength: { value: 10, message: "Invalid Phone Number" },
              })}
            />
            {errors.phoneNo && (
              <span className="text-xs sm:text-sm text-yellow-100 mt-1">
                {errors.phoneNo.message}
              </span>
            )}
          </div>

          {/* Message Field */}
          <div className="flex flex-col gap-2">
            <label className="text-left text-richblack-5 mb-1 font-inter text-sm sm:text-base">
              Message *
            </label>
            <textarea
              name="message"
              id="message"
              rows="6"
              placeholder="Enter your message here"
              className="form-textarea-responsive"
              {...register("message", { required: true })}
            />
            {errors.message && (
              <span className="text-xs sm:text-sm text-yellow-100 mt-1">
                Please enter your Message.
              </span>
            )}
          </div>

          {/* Submit Button */}
          <button
            disabled={loading}
            type="submit"
            className="w-full sm:w-auto sm:px-12 py-3 sm:py-4 rounded-md font-semibold text-black bg-gold-500 hover:bg-gold-400 transition-all duration-300 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 focus:ring-offset-richblack-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Sending...' : 'Send Message'}
          </button>
        </form>
      </div>
    </div>
  )
}
