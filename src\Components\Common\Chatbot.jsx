import React, { useState } from 'react';
import './Chatbot.css';
import { FaRobot } from 'react-icons/fa';
import { IoMdClose } from 'react-icons/io';

const FancyChatbot = () => {
    const [isOpen, setIsOpen] = useState(false);
    const toggleChat = () => setIsOpen(!isOpen);

    return (
        <>
            {/* Chatbot Icon with dynamic transition */}
            <div
                className={`chatbotIcon ${isOpen ? 'active' : ''}`}
                onClick={toggleChat}
            >
                <div className="icon-container">
                    <FaRobot className={`bot-icon ${isOpen ? 'hidden' : 'visible'}`} />
                    <IoMdClose className={`close-icon ${isOpen ? 'visible' : 'hidden'}`} />
                </div>
            </div>

            {/* Chat Container */}
            {isOpen && (
                <div className="chatContainer">
                    <div className="chatHeader">
                        <div className="chatHeaderTitle">
                            <FaRobot className="botIcon" />
                            <div>
                                <h3>Chatbot</h3>
                                <p>Ask me anything!</p>
                            </div>
                        </div>
                        <button className="closeButton" onClick={toggleChat}>
                            <IoMdClose />
                        </button>
                    </div>

                    {/* Chat content */}
                    <div style={{ padding: '16px', color: 'white' }}>
                        <p>This is a placeholder for chatbot content.</p>
                    </div>
                </div>
            )}
        </>
    );
};

export default FancyChatbot;
