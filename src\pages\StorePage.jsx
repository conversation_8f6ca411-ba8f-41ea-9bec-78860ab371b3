import { useState } from 'react';
import { Footer } from '../Components/Common/Footer';
import Navbar from '../Components/Common/Navbar';
import Breadcrumb from '../Components/Common/Breadcrumb';
import { FaStar, FaHeart, FaRegHeart } from 'react-icons/fa';

// Import header image and product images
import headerImg from '../assets/Images/Header_Image/150295.jpg';
import sofaImg from '../assets/Images/furniture_selection.jpeg';
import lightImg from '../assets/Images/custom_interior_design.jpeg';
import artImg from '../assets/Images/About1.webp';
import tableImg from '../assets/Images/About2.webp';
import wardrobeImg from '../assets/Images/space_planning.jpeg';
import lampImg from '../assets/Images/home_renovation.jpeg';

// Store items data
const storeItems = [
  {
    id: 1,
    name: "Modern Scandinavian Sofa",
    description: "Elegant 3-seater sofa with premium fabric upholstery and solid wood legs.",
    rating: 4.8,
    reviewCount: 124,
    image: sofaImg,
    slug: "modern-scandinavian-sofa"
  },
  {
    id: 2,
    name: "Geometric Brass Pendant Light",
    description: "Modern geometric pendant light with brass finish, perfect for dining areas.",
    rating: 4.7,
    reviewCount: 86,
    image: lightImg,
    slug: "geometric-brass-pendant-light"
  },
  {
    id: 3,
    name: "Abstract Canvas Art Set",
    description: "Set of 3 abstract canvas prints with gold accents, perfect for modern interiors.",
    rating: 4.5,
    reviewCount: 52,
    image: artImg,
    slug: "abstract-canvas-art-set"
  },
  {
    id: 4,
    name: "Marble Coffee Table",
    description: "Elegant coffee table with genuine marble top and gold-finished metal base.",
    rating: 4.8,
    reviewCount: 31,
    image: tableImg,
    slug: "marble-coffee-table"
  },
  {
    id: 5,
    name: "Minimalist Wooden Wardrobe",
    description: "Spacious 3-door wardrobe with modern design and ample storage space.",
    rating: 4.9,
    reviewCount: 156,
    image: wardrobeImg,
    slug: "minimalist-wooden-wardrobe"
  },
  {
    id: 6,
    name: "Designer Floor Lamp",
    description: "Contemporary floor lamp with adjustable height and warm LED lighting.",
    rating: 4.6,
    reviewCount: 78,
    image: lampImg,
    slug: "designer-floor-lamp"
  }
];

const StorePage = () => {
  const [wishlist, setWishlist] = useState([]);

  const toggleWishlist = (id) => {
    setWishlist(prev =>
      prev.includes(id) ? prev.filter(itemId => itemId !== id) : [...prev, id]
    );
  };

  const renderRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <FaStar 
          key={i} 
          className={i <= fullStars ? "text-yellow-500" : "text-gray-400"} 
        />
      );
    }
    return <div className="flex">{stars}</div>;
  };

  return (
    <div className="bg-richblack-900 min-h-screen flex flex-col">
      <Navbar />
      <Breadcrumb />

      {/* Hero Section */}
      <div className="relative h-[400px] w-full overflow-hidden">
        <div className="absolute inset-0 bg-black/70 z-10"></div>
        <img
          src={headerImg}
          alt="Interior Design Store"
          className="h-full w-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4">
          <h1 className="text-3xl md:text-5xl font-inter font-bold text-white mb-4">
            Premium Furniture Store
          </h1>
          <p className="text-lg text-gray-200 max-w-3xl mb-6">
            Discover our curated collection of premium furniture and decor for your dream space
          </p>
        </div>
      </div>

      {/* Products */}
      <main className="max-w-6xl mx-auto px-4 py-12 flex-1">
        <h2 className="text-2xl font-semibold text-white mb-6">All Products</h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {storeItems.map(item => (
            <div key={item.id} className="bg-richblack-800 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden flex flex-col border border-richblack-700 hover:border-gold-500 group cursor-pointer"
                 onClick={() => window.location.href = `/store/product/${item.slug}`}>
              <div className="relative aspect-square w-full overflow-hidden">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-full object-cover transform transition-transform duration-500 group-hover:scale-105"
                />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleWishlist(item.id);
                  }}
                  className="absolute top-3 right-3 bg-richblack-800/80 p-2 rounded-full shadow hover:bg-richblack-700"
                >
                  {wishlist.includes(item.id) ? <FaHeart className="text-red-500" /> : <FaRegHeart className="text-white" />}
                </button>
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                </div>
              </div>

              <div className="p-5">
                <h3 className="text-lg font-medium text-white mb-2 group-hover:text-gold-400 transition-colors duration-300">{item.name}</h3>
                <div className="flex items-center gap-2">
                  {renderRating(item.rating)}
                  <span className="text-xs text-gray-400">({item.rating})</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </main>



      <Footer />
    </div>
  );
};

export default StorePage;
