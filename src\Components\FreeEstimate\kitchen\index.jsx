import { Route, Routes } from "react-router-dom"
import { KitchenLayout } from "./KitchenLayout"
import { Measurement } from "./Measurement"
import KitchenPackgae from "./KitchenPackgae"
import Material from "./components/Material/Material"
import { MaterialPage } from "./MaterialPage"
import Getestimate from "./Getestimate"

const KitchenRoutes=()=>{


    return(
        <Routes>
           <Route path='/KitchenLayout' element={<KitchenLayout/>} ></Route>
           <Route path='/KitchenLayout/measurement' element={<Measurement/>} ></Route> 
           <Route path='/KitchenLayout/measurement/package' element={<KitchenPackgae/>} ></Route> 
           <Route path='/KitchenLayout/measurement/package/material' element={< MaterialPage/>} ></Route> 
           <Route path='/KitchenLayout/measurement/package/material/getestimate' element={< Getestimate/>} ></Route> 

           

        </Routes>
    )
}

export default KitchenRoutes