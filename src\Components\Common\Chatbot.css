/* Chatbot Icon */
.chatbotIcon {
    position: fixed;
    bottom: 90px;
    right: 24px;
    background: linear-gradient(135deg, #d4a940, #9a7b2e);
    color: white;
    font-size: 24px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
    transition: all 0.3s ease;
    z-index: 45;
    animation: pulse-attention 2s infinite;
}

.chatbotIcon:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.chatbotIcon.light-theme {
    background: linear-gradient(135deg, #d4a940, #b8922f);
    color: #333;
}

/* Chat Container */
.chatContainer {
    position: fixed;
    bottom: 90px;
    right: 24px;
    width: 350px;
    height: 500px;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    z-index: 45;
    animation: fadeInUp 0.4s ease;
    background: rgba(30, 30, 30, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chatContainer.light-theme {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Chat Header */
.chatHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: linear-gradient(to right, #d4a940, #9a7b2e);
    color: white;
}

.chatHeaderTitle {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chatHeaderTitle h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chatHeaderTitle p {
    margin: 0;
    font-size: 12px;
    opacity: 0.8;
}

.botIcon {
    font-size: 24px;
}

.closeButton {
    background: transparent;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border-radius: 50%;
    transition: background 0.2s;
}

.closeButton:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse-attention {
    0% {
        box-shadow: 0 0 0 0 rgba(212, 169, 64, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(212, 169, 64, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(212, 169, 64, 0);
    }
}
