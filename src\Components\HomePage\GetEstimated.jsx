import { useState, forwardRef } from 'react';
import { Link, useNavigate } from 'react-router-dom'; // <-- Import useNavigate
import img1 from '../../assets/GetEstimated/plan.png';
import img2 from '../../assets/GetEstimated/kitchen.png';
import img3 from '../../assets/GetEstimated/wardrobe.png';

export const GetEstimated = forwardRef((props, ref) => {
  const [selectedSection, setSelectedSection] = useState('Full Home');
  const navigate = useNavigate(); // <-- React Router hook

  const options = [
    {
      label: 'Full Home Interior',
      description: 'Know the estimate price for your full home interiors',
      value: 'Full Home',
      icon: img1,
      path: '/home-interior/bhkselection', // <-- path added
    },
    {
      label: 'Kitchen',
      description: 'Get an approximate costing for your kitchen interior.',
      value: 'Kitchen',
      icon: img2,
      path: '/kitchen/KitchenLayout', // <-- path added
    },
    {
      label: 'Wardrobe',
      description: 'Our estimate for your dream wardrobe',
      value: 'Wardrobe',
      icon: img3,
      path: '/Wardrobe/HeightSelection', // <-- path added
    },
  ];

  const handleClick = (value, path) => {
    setSelectedSection(value);
    navigate(path); // <-- Navigate to relevant route
  };

  return (
    <div ref={ref} className="text-center px-4 py-8">
      <h2 className="text-3xl font-bold text-richblack-200 font-inter">
        Get the estimate for your <span className="text-red-500">{selectedSection}</span>
      </h2>
      <p className="text-md mt-2 text-gray-100 font-inter">
        Calculate the approximate cost of doing up your home interiors
      </p>

      <div className="flex flex-wrap justify-center gap-8 mt-10">
        {options.map((item, index) => (
          <div
            key={index}
            className="w-80 bg-gray-800 shadow-lg rounded-xl p-8 transform transition-transform duration-200 hover:scale-105 hover:shadow-2xl cursor-pointer"
            onClick={() => handleClick(item.value, item.path)}
          >
            <div className="text-center flex flex-col items-center">
              <img
                src={item.icon}
                alt={item.label}
                className="w-24 h-24 object-contain mb-5"
              />
              <h3 className="font-semibold text-xl mb-2 text-white">{item.label}</h3>
              <p className="text-gray-400 text-base">{item.description}</p>
            </div>
            <Link to={item.path}>
              <button className="mt-6 bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-2.5 px-4 rounded w-full transition-colors duration-200">
                CALCULATE →
              </button>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
});
