import { useState } from 'react';
import { <PERSON>a<PERSON>ye, FaEyeSlash, FaLock, FaEnvelope } from 'react-icons/fa';
import { Link } from 'react-router-dom';

export const LoginForm = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [showPassword, setShowPassword] = useState(false);

    const submitHandler = async (e) => {
        e.preventDefault();
        // Handle login logic here
    };

    return (
        <div className="w-full">
            <form onSubmit={submitHandler} className="w-full">
                <div className="w-full">
                    <div className="flex flex-col gap-y-6 w-full">
                        {/* Email Field */}
                        <div className="relative">
                            <label>
                                <p className="text-left text-richblack-5 mb-2 font-medium">
                                    Email Address<sup className="text-gold-500">*</sup>
                                </p>
                                <div className="relative">
                                    <input
                                        className="w-full py-3 px-4 pl-10 text-white rounded-md outline-none border border-richblack-700 bg-richblack-800 focus:border-gold-500 transition-all"
                                        onChange={(e) => setEmail(e.target.value)}
                                        type="email"
                                        placeholder="Enter your email"
                                        name="email"
                                        required
                                    />
                                    <FaEnvelope className="absolute left-3 top-1/2 -translate-y-1/2 text-richblack-400" />
                                </div>
                            </label>
                        </div>

                        {/* Password Field */}
                        <div className="relative">
                            <label>
                                <p className="text-left text-richblack-5 mb-2 font-medium">
                                    Password<sup className="text-gold-500">*</sup>
                                </p>
                                <div className="relative">
                                    <input
                                        className="w-full py-3 px-4 pl-10 text-white rounded-md outline-none border border-richblack-700 bg-richblack-800 focus:border-gold-500 transition-all"
                                        onChange={(e) => setPassword(e.target.value)}
                                        type={showPassword ? 'text' : 'password'}
                                        placeholder="Enter your password"
                                        name="password"
                                        required
                                    />
                                    <FaLock className="absolute left-3 top-1/2 -translate-y-1/2 text-richblack-400" />
                                    <div
                                        className="absolute right-3 top-1/2 -translate-y-1/2 text-richblack-300 cursor-pointer hover:text-gold-500 transition-colors"
                                        onClick={() => setShowPassword(!showPassword)}
                                    >
                                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                                    </div>
                                </div>
                            </label>
                        </div>

                        {/* Forgot Password Link */}
                        <div className="text-right">
                            <Link to="/forgot-password" className="text-gold-500 hover:text-gold-400 transition-colors text-sm">
                                Forgot Password?
                            </Link>
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            className="w-full py-3 rounded-md font-semibold text-richblack-900 bg-gold-500 hover:bg-gold-400 transition-colors mt-2"
                        >
                            Sign In
                        </button>

                        {/* Sign Up Link */}
                        <p className="text-center text-richblack-300 mt-4">
                            Don't have an account?
                            <Link to="/signup" className="text-gold-500 hover:text-gold-400 ml-1 font-medium">
                                Sign Up
                            </Link>
                        </p>
                    </div>
                </div>
            </form>
        </div>
    );
};
