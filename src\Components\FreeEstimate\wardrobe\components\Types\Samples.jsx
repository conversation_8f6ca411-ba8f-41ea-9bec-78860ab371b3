import React, { useState } from 'react'
import { Link } from 'react-router-dom';
const doorOptions = [
    {
        type: 'Sliding',
        description: 'Movable doors that slide horizontally along a metal rail and save floor space.',
        image: 'https://images.unsplash.com/photo-1598300058500-6b0e6b9c8c1c?auto=format&fit=crop&w=800&q=80',
        tip: 'Make a style statement even in a compact space.',
    },
    {
        type: 'Swing',
        description: 'Movable doors that swing out to open, giving better visibility and storage space.',
        image: 'https://images.unsplash.com/photo-1620121692029-d088224ddc4a?auto=format&fit=crop&w=800&q=80',
        tip: 'A cost-effective option that never goes out of style.',
    },
];

const Samples = () => {
    const [selected, setSelected] = useState('Sliding');
    return (
        <div className=" bg-richblack-900  mb-6 flex items-center justify-center px-4">
            <div className="max-w-xl w-full bg-richblack-800 text-richblack-25 rounded-xl shadow-lg p-8">
                <h2 className="text-2xl font-bold mb-6 text-center font-inter text-richblack-25">Choose your wardrobe door style</h2>

                <div className="space-y-6">
                    {doorOptions.map((option) => (
                        <div
                            key={option.type}
                            onClick={() => setSelected(option.type)}
                            className={`rounded-xl border-2 cursor-pointer transition-all duration-300 ${selected === option.type
                                ? 'border-yellow-100 bg-richblack-700'
                                : 'border-richblack-600 bg-richblack-800'
                                }`}
                        >
                            <div className="p-4">
                                <div className="flex items-center gap-3 mb-2">
                                    <div
                                        className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${selected === option.type ? 'border-yellow-100' : 'border-richblack-300'
                                            }`}
                                    >
                                        {selected === option.type && (
                                            <div className="w-2.5 h-2.5 bg-yellow-100 rounded-full"></div>
                                        )}
                                    </div>
                                    <h3 className="text-lg font-semibold">{option.type}</h3>
                                </div>
                                <p className="text-sm text-richblack-300 mb-4">{option.description}</p>
                                <img src={option.image} alt={option.type} className="w-full rounded-md mb-4" />
                                <p className="text-richblack-200 text-sm italic">{option.tip}</p>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="flex justify-between items-center mt-10">
                    <button className="text-yellow-300 hover:text-yellow-300 font-medium transition-all">Back</button>
                    <Link to='materialtype'>
                        <button className="bg-yellow-100 hover:bg-yellow-200 text-richblack-900 font-semibold px-6 py-2 rounded-full transition-all">
                            Next
                        </button>
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default Samples