
import Img from '../../assets/Images/login.png'
const QuoteForm = () => {
  return (
    <div className="flex flex-col rounded-lg lg:flex-row justify-between px-4 lg:px-24 py-12 ">
      
      {/* Left side with image */}
      <div className="md:w-1/2 w-full h-[300px] md:h-auto">
        <img
          src={Img} // <-- Make sure this exists inside /public/assets/
          alt="Interior Design"
          className="w-full h-full object-cover"
        />
      </div>

      {/* Right side with form */}
      <div className="md:w-1/2 w-full bg-gray-800 flex items-center font-inter justify-center px-6 py-12">
        <div className="max-w-md w-full text-white">
          <h1 className="text-3xl font-bold mb-3 text-center">Designs for Every Budget</h1>
          <p className="text-sm mb-8 text-center">Get your dream home today. Let our experts help you</p>

          <form className="space-y-5">
            <input
              type="text"
              placeholder="Name"
              className=" p-3 rounded-mdtext-white rounded-md shadow-richblack-500 shadow-sm outline-none bg-richblack-800 w-full"
            />
            <input
              type="email"
              placeholder="Email"
               className=" p-3 rounded-mdtext-white rounded-md shadow-richblack-500 shadow-sm outline-none bg-richblack-800 w-full"
            />
            <div className="flex items-center border border-gray-300 rounded-md overflow-hidden">
              <span className="px-3  text-black text-sm">🇮🇳</span>
              <input
                type="tel"
                placeholder="Phone Number"
                className=" p-3 rounded-mdtext-white rounded-md shadow-richblack-500 shadow-sm outline-none bg-richblack-800 w-full"
              />
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="whatsapp" className="accent-yellow-300" />
              <label htmlFor="whatsapp" className="text-sm">
                Send me updates on WhatsApp
              </label>
            </div>
            <input
              type="text"
              placeholder="Property Name"
              className=" p-3 rounded-mdtext-white rounded-md shadow-richblack-500 shadow-sm outline-none bg-richblack-800 w-full"
            />
            <button
              type="submit"
              className="w-full p-3 rounded-md bg-yellow-300 text-white font-semibold hover:bg-yellow-500 transition"
            >
              GET FREE QUOTE
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default QuoteForm;
