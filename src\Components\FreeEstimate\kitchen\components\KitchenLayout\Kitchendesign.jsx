import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const kitchenLayouts = [
  {
    name: 'L-shaped',
    img: 'https://dummyimage.com/300x200/1a202c/ffffff&text=L+Shaped',
  },
  {
    name: 'Straight',
    img: 'https://dummyimage.com/300x200/1a202c/ffffff&text=Straight',
  },
  {
    name: 'U-shaped',
    img: 'https://dummyimage.com/300x200/1a202c/ffffff&text=U+Shaped',
  },
  {
    name: 'Parallel',
    img: 'https://dummyimage.com/300x200/1a202c/ffffff&text=Parallel',
  },
];

export const Kitchendesign = ({ onSelect }) => {
  const [selected, setSelected] = useState('');

  const handleSelect = (layout) => {
    setSelected(layout);
    if (onSelect) onSelect(layout);
  };

  return (
    <div className="mb-5 max-w-4xl mx-auto mt-12 p-6 sm:p-10 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white rounded-3xl shadow-2xl border border-gray-700">
      <h2 className="text-3xl sm:text-4xl font-bold font-inter text-center text-richblack-25 mb-2">
        Select the layout of your kitchen
      </h2>
      <p className="text-center text-sm text-gray-400 mb-8">
        Want to know more?{' '}
        <a href="#" className="text-yellow-300 hover:underline">
          Check here
        </a>
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        {kitchenLayouts.map((layout) => (
          <div
            key={layout.name}
            onClick={() => handleSelect(layout.name)}
            className={`cursor-pointer bg-gray-800 border rounded-2xl overflow-hidden shadow-lg transition-transform transform hover:scale-105 ${selected === layout.name ? 'border-yellow-300' : 'border-gray-700'
              }`}
          >
            <div className="bg-gray-900 h-36 flex items-center justify-center">
              <img src={layout.img} alt={layout.name} className="h-20" />
            </div>
            <div className="text-center py-3 text-white font-medium">{layout.name}</div>
          </div>
        ))}
      </div>

      <div className="mt-10 flex justify-between">
        <button className="text-yellow-300 hover:text-yellow-300 font-semibold">BACK</button>
        <Link to={selected ? 'measurement' : '#'}> {/* Replace '/next-page' with actual path */}
          <button
            disabled={!selected}
            className={`px-6 py-3 bg-yellow-300 rounded-xl font-semibold shadow-md transition-all ${selected
                ? 'hover:from-yellow-500 hover:to-yellow-700 text-black'
                : 'text-gray-400 cursor-not-allowed'
              }`}
          >
            NEXT
          </button>
        </Link>
      </div>
    </div>
  );
};
