import { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  FaHeart,
  FaRegHeart,
  FaArrowRight,
  FaFacebook,
  FaTwitter,
  FaPinterest,
  FaShareAlt,
  FaWhatsapp,
  FaInstagram,
  FaLinkedinIn
} from 'react-icons/fa';

const DesignCard = ({
  image,
  title,
  description,
  category,
  price,
  link,
  isFavorite = false,
  onFavoriteToggle,
  theme = 'dark',
  onClick
}) => {
  const [showShareOptions, setShowShareOptions] = useState(false);

  const handleFavoriteClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onFavoriteToggle) {
      onFavoriteToggle();
    }
  };

  const handleShareClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setShowShareOptions(!showShareOptions);
  };

  const handleSocialShare = (platform, e) => {
    e.preventDefault();
    e.stopPropagation();

    const shareUrl = window.location.origin + (link || '');
    const shareText = `Check out this amazing design: ${title}`;

    let shareLink;
    switch (platform) {
      case 'facebook':
        shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
        break;
      case 'twitter':
        shareLink = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;
        break;
      case 'pinterest':
        shareLink = `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(shareUrl)}&media=${encodeURIComponent(image)}&description=${encodeURIComponent(shareText)}`;
        break;
      case 'whatsapp':
        shareLink = `https://api.whatsapp.com/send?text=${encodeURIComponent(shareText + ' ' + shareUrl)}`;
        break;
      case 'linkedin':
        shareLink = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
        break;
      case 'instagram':
        // Instagram doesn't have a direct sharing API, so we'll just open Instagram
        // Usually, you'd need to use their API for business accounts
        alert('Copy the link and share it on Instagram: ' + shareUrl);
        return;
      default:
        shareLink = shareUrl;
    }

    window.open(shareLink, '_blank', 'width=600,height=400');
    setShowShareOptions(false);
  };

  return (
    <div className={`group overflow-hidden rounded-md border ${theme === 'light'
        ? 'border-gray-200 bg-white'
        : 'border-richblack-700 bg-richblack-800'
      } shadow-lg transition-all duration-300 hover:shadow-2xl hover:translate-y-[-5px] m-2`}>
      {/* Image Container with Overlay */}
      <div className="relative overflow-hidden">
        {/* Category Tag */}
        <div className={`absolute top-4 left-4 z-10 rounded-sm px-3 py-1 text-xs font-medium uppercase tracking-wider shadow-md ${theme === 'light'
            ? 'bg-gold-400 text-richblack-900'
            : 'bg-gold-500 text-richblack-900'
          }`}>
          {category}
        </div>

        {/* Favorite Button */}
        <button
          onClick={handleFavoriteClick}
          className={`absolute top-4 right-4 z-10 rounded-full p-2 shadow-md transition-all duration-300 ${theme === 'light'
              ? 'bg-white text-richblack-900 hover:bg-gold-400 hover:text-white'
              : 'bg-richblack-900 text-white hover:bg-gold-500 hover:text-richblack-900'
            }`}
          aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
        >
          {isFavorite ? <FaHeart className="text-gold-400" /> : <FaRegHeart />}
        </button>

        {/* Share Button */}
        <button
          onClick={handleShareClick}
          className={`absolute top-4 right-16 z-10 rounded-full p-2 shadow-md transition-all duration-300 ${theme === 'light'
              ? 'bg-white text-richblack-900 hover:bg-gold-400 hover:text-white'
              : 'bg-richblack-900 text-white hover:bg-gold-500 hover:text-richblack-900'
            } ${showShareOptions ? 'bg-gold-500 text-richblack-900' : ''}`}
          aria-label="Share this design"
        >
          <FaShareAlt className={showShareOptions ? 'text-richblack-900' : ''} />
        </button>

        {/* Social Share Options */}
        {showShareOptions && (
          <div className="absolute top-16 right-4 z-20 bg-richblack-800 rounded-md shadow-xl p-3 flex flex-wrap gap-2 border border-gold-900/30 animate-fade-in backdrop-blur-sm bg-opacity-95 w-[180px]">
            <button
              onClick={(e) => handleSocialShare('facebook', e)}
              className="p-2 rounded-full bg-[#1877F2] text-white hover:bg-[#0E65E0] transition-all duration-300 transform hover:scale-110"
              aria-label="Share on Facebook"
            >
              <FaFacebook />
            </button>
            <button
              onClick={(e) => handleSocialShare('instagram', e)}
              className="p-2 rounded-full bg-gradient-to-br from-[#833AB4] via-[#FD1D1D] to-[#FCAF45] text-white hover:from-[#7232A8] hover:via-[#E51A1A] hover:to-[#EEA03D] transition-all duration-300 transform hover:scale-110"
              aria-label="Share on Instagram"
            >
              <FaInstagram />
            </button>
            <button
              onClick={(e) => handleSocialShare('twitter', e)}
              className="p-2 rounded-full bg-[#1DA1F2] text-white hover:bg-[#0C8BD9] transition-all duration-300 transform hover:scale-110"
              aria-label="Share on Twitter"
            >
              <FaTwitter />
            </button>
            <button
              onClick={(e) => handleSocialShare('whatsapp', e)}
              className="p-2 rounded-full bg-[#25D366] text-white hover:bg-[#20BD5C] transition-all duration-300 transform hover:scale-110"
              aria-label="Share on WhatsApp"
            >
              <FaWhatsapp />
            </button>
            <button
              onClick={(e) => handleSocialShare('pinterest', e)}
              className="p-2 rounded-full bg-[#E60023] text-white hover:bg-[#D1001F] transition-all duration-300 transform hover:scale-110"
              aria-label="Share on Pinterest"
            >
              <FaPinterest />
            </button>
            <button
              onClick={(e) => handleSocialShare('linkedin', e)}
              className="p-2 rounded-full bg-[#0A66C2] text-white hover:bg-[#0959AB] transition-all duration-300 transform hover:scale-110"
              aria-label="Share on LinkedIn"
            >
              <FaLinkedinIn />
            </button>
          </div>
        )}

        {/* Main Image */}
        <img
          src={image}
          alt={title}
          className="h-64 w-full object-cover transition-transform duration-500 group-hover:scale-105"
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
      </div>

      {/* Content */}
      <div className={`p-6 ${theme === 'light'
          ? 'bg-gradient-to-b from-white to-gray-50'
          : 'bg-gradient-to-b from-richblack-800 to-richblack-900'
        }`}>
        <h3 className={`mb-3 text-xl font-inter font-semibold ${theme === 'light' ? 'text-richblack-900' : 'text-white'
          }`}>
          {title}
        </h3>

        <p className={`mb-5 text-sm line-clamp-2 ${theme === 'light' ? 'text-richblack-700' : 'text-gray-300'
          }`}>
          {description}
        </p>

        {/* Divider */}
        <div className={`h-px w-full mb-5 ${theme === 'light' ? 'bg-gray-200' : 'bg-richblack-700'
          }`}></div>

        {/* Price and Action Button */}
        <div className="flex items-center justify-between mb-4">
          {price && (
            <div className={`text-lg font-medium ${theme === 'light' ? 'text-richblack-900' : 'text-gold-300'
              }`}>
              ₹{price}
            </div>
          )}

          {onClick ? (
            <button
              onClick={onClick}
              className={`flex items-center gap-2 text-sm font-medium px-4 py-2 rounded-sm transition-all duration-300 ${theme === 'light'
                  ? 'bg-gold-50 text-gold-600 hover:bg-gold-100 hover:text-gold-700'
                  : 'bg-richblack-700 text-gold-400 hover:bg-richblack-600 hover:text-gold-300'
                }`}
            >
              Get Quote <FaArrowRight className="text-xs ml-1" />
            </button>
          ) : (
            <Link
              to={link}
              className={`flex items-center gap-2 text-sm font-medium px-4 py-2 rounded-sm transition-all duration-300 ${theme === 'light'
                  ? 'bg-gold-50 text-gold-600 hover:bg-gold-100 hover:text-gold-700'
                  : 'bg-richblack-700 text-gold-400 hover:bg-richblack-600 hover:text-gold-300'
                }`}
            >
              View Details <FaArrowRight className="text-xs ml-1" />
            </Link>
          )}
        </div>

        {/* Social Media Icons */}
        <div
          className={`flex items-center justify-center gap-5 pt-6 border-t ${theme === "light" ? "border-gray-200" : "border-richblack-700"
            }`}
        >
          {/* Facebook */}
          <button
            onClick={(e) => handleSocialShare("facebook", e)}
            className="group relative p-3 rounded-full transition-all duration-300 transform hover:scale-110"
            aria-label="Share on Facebook"
          >
            <div className="absolute inset-0 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-300 ring-2 ring-[#1877F2]/30 group-hover:ring-4"></div>
            <FaFacebook
              className={`text-2xl z-10 relative ${theme === "light" ? "text-[#1877F2]" : "text-[#4293FF]"
                }`}
            />
          </button>

          {/* Instagram */}
          <button
            onClick={(e) => handleSocialShare("instagram", e)}
            className="group relative p-3 rounded-full transition-all duration-300 transform hover:scale-110"
            aria-label="Share on Instagram"
          >
            <div className="absolute inset-0  rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-300 ring-2 ring-pink-300/30 group-hover:ring-4"></div>
            <FaInstagram
              className={`text-2xl z-10 relative ${theme === "light" ? "text-[#E1306C]" : "text-[#FF6F91]"
                }`}
            />
          </button>

          {/* WhatsApp */}
          <button
            onClick={(e) => handleSocialShare("whatsapp", e)}
            className="group relative p-3 rounded-full transition-all duration-300 transform hover:scale-110"
            aria-label="Share on WhatsApp"
          >
            <div className="absolute inset-0  rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-300 ring-2 ring-[#25D366]/30 group-hover:ring-4"></div>
            <FaWhatsapp
              className={`text-2xl z-10 relative ${theme === "light" ? "text-[#25D366]" : "text-[#4AE388]"
                }`}
            />
          </button>

          {/* Pinterest */}
          <button
            onClick={(e) => handleSocialShare("pinterest", e)}
            className="group relative p-3 rounded-full transition-all duration-300 transform hover:scale-110"
            aria-label="Share on Pinterest"
          >
            <div className="absolute inset-0 bounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-300 ring-2 ring-[#E60023]/30 group-hover:ring-4"></div>
            <FaPinterest className='text-red-500'
              
            />
          </button>
        </div>

      </div>
    </div>
  );
};

export default DesignCard;
