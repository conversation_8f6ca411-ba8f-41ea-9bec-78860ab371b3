import React, { useState } from 'react'

export const Template = ({ title, currency, description, image, features, items }) => {
    const initialCount = items.reduce((acc, item) => ({ ...acc, [item.name]: 0 }), {});
    const [itemCount, setItemCount] = useState(initialCount);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setItemCount((prev) => ({
            ...prev,
            [name]: parseInt(value) || 0,
        }));
    };

    const totalPrice = items.reduce((total, item) => {
        return total + itemCount[item.name] * item.price;
    }, 0);

    return (
        <div className="p-4">
        <label
          htmlFor={`package-${title}`}
          className="flex flex-col font-inter text-richblack-25 items-center hover:scale-105 transition-all duration-200 cursor-pointer"
        >
          <div className="border rounded-xl shadow-md p-7 w-full max-w-sm">
            <div className="flex items-start mb-3">
              <input
                type="radio"
                id={`package-${title}`}
                name="package"
                className="mt-1 mr-3"
              />
              <div>
                <h3 className="text-base font-semibold">
                  {title} <span className="">({currency})</span>
                </h3>
                <p className="text-richblack-50">{description}</p>
              </div>
            </div>
      
            <img
              src={image}
              alt={title}
              className="rounded-md mb-3 w-full h-32 object-cover"
            />
      
            <ul className="text-sm text-gray-700 space-y-1 mb-2">
              {features.map((feature, index) => (
                <li className="text-richblack-25 font-inter" key={index}>
                  ✅ {feature}
                </li>
              ))}
            </ul>
          </div>
        </label>
      </div>
      
    );
};
