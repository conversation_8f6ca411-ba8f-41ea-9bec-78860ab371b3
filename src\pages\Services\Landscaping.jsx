
import { Link } from 'react-router-dom';
import Navbar from '../../Components/Common/Navbar';
import { Footer } from '../../Components/Common/Footer';
import Breadcrumb from '../../Components/Common/Breadcrumb';
import { FaCheck, FaPhone, FaEnvelope, FaLeaf, FaTree, FaSeedling } from 'react-icons/fa';

// Import images
import headerImg from '../../assets/Images/Header_Image/2712435.jpg';
import dummyImage1 from '../../assets/Images/About1.webp';

const Landscaping = () => {
  const features = [
    'Garden Design & Planning',
    'Plant Selection & Sourcing',
    'Irrigation System Setup',
    'Hardscape Installation',
    'Lawn Development',
    'Tree Plantation',
    'Maintenance Services',
    'Seasonal Care'
  ];

  const services = [
    {
      icon: <FaLeaf className="text-gold-500 text-2xl" />,
      title: 'Garden Design',
      description: 'Beautiful garden layouts with carefully selected plants and landscape features'
    },
    {
      icon: <FaTree className="text-gold-500 text-2xl" />,
      title: 'Tree Plantation',
      description: 'Strategic tree placement for shade, privacy, and environmental benefits'
    },
    {
      icon: <FaSeedling className="text-gold-500 text-2xl" />,
      title: 'Lawn Development',
      description: 'Lush green lawns with proper soil preparation and grass selection'
    }
  ];

  return (
    <div className="bg-richblack-900 min-h-screen">
      <Navbar />
      <Breadcrumb />

      {/* Hero Section */}
      <div className="relative h-[400px] w-full overflow-hidden">
        <div className="absolute inset-0 bg-black/70 z-10"></div>
        <img
          src={headerImg}
          alt="Street & Garden Landscaping"
          className="h-full w-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4">
          <h1 className="text-3xl md:text-5xl font-inter font-bold text-white mb-4">
            Street & Garden Landscaping
          </h1>
          <p className="text-lg text-gray-200 max-w-3xl mb-6">
            Beautiful outdoor spaces with professional landscaping design for streets, gardens, and outdoor areas
          </p>
          <Link
            to="/contact"
            className="px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
          >
            Get Quote
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        {/* Service Overview */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold text-white mb-6">
              Transform Your Outdoor Spaces
            </h2>
            <p className="text-gray-300 mb-6">
              Our landscaping services create stunning outdoor environments that enhance 
              the beauty and value of your property. From intimate garden spaces to 
              large-scale street landscaping projects.
            </p>
            <p className="text-gray-300 mb-8">
              We combine aesthetic design with practical functionality to create 
              sustainable landscapes that thrive in local climate conditions.
            </p>
            
            {/* Features List */}
            <div className="grid grid-cols-2 gap-3">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <FaCheck className="text-gold-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-6">
            <img
              src={dummyImage1}
              alt="Landscaping Design"
              className="w-full h-64 object-cover rounded-lg"
            />
            <div className="bg-richblack-800 p-6 rounded-lg">
              <h3 className="text-xl font-bold text-white mb-4">Our Approach</h3>
              <ul className="space-y-3 text-gray-300">
                <li>• Site analysis and soil testing</li>
                <li>• Climate-appropriate plant selection</li>
                <li>• Water-efficient irrigation systems</li>
                <li>• Sustainable maintenance practices</li>
                <li>• Year-round beauty planning</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Our Landscaping Services
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-richblack-800 p-6 rounded-lg text-center hover:bg-richblack-700 transition-all duration-300">
                <div className="mb-4 flex justify-center">
                  {service.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-300 text-sm">
                  {service.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-richblack-800 to-richblack-900 rounded-lg p-8 md:p-12 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Ready to Transform Your Landscape?</h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Contact us for a consultation and let us create a beautiful outdoor space for you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="inline-flex items-center gap-2 px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
            >
              <FaPhone className="text-sm" />
              Get Free Quote
            </Link>
            <a
              href="tel:9599222158"
              className="inline-flex items-center gap-2 px-8 py-3 bg-transparent border border-gold-500 text-gold-500 rounded-sm font-medium hover:bg-gold-500/10 transition-all duration-300"
            >
              <FaEnvelope className="text-sm" />
              Call: 9599222158
            </a>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Landscaping;
