
import img1 from '../assets/Images/About1.webp'
import img2 from '../assets/Images/About2.webp'
import img3 from '../assets/Images/About3.webp'
import Story from '../assets/Images/Story.png'

import { Quote } from '../Components/About/Quote';
import { Form } from '../Components/Common/Form';
import { Footer } from '../Components/Common/Footer';
import { MapView } from '../Components/HomePage/Map';
import Navbar from '../Components/Common/Navbar';
import Breadcrumb from '../Components/Common/Breadcrumb';
import FloatingButtons from '../Components/Common/FloatingButtons';
import DelayedModalForm from '../Components/Common/DelayedModalForm';
import CounterSection from '../Components/About/CounterSection';


const About = () => {

  return (
    <div>
      <Navbar />
      <Breadcrumb />
      <div className="p-8 bg-gradient-to-b from-gray-900 to-richblack-900 text-white">
        <h1 className="text-4xl font-bold text-center  m
        t-2 mb-12">Transforming Spaces,<span className='text-blue-300 font-bold mx-auto'>Enriching Lives</span> </h1>
        <p className="font-inter font-semibold text-lg mb-16 max-w-4xl text-center mx-auto">At Sparrow Interiors, we believe in creating spaces that reflect your personality and style. Our team of expert designers is dedicated to transforming your home into a sanctuary of beauty and comfort.</p>
        <div className="flex flex-col md:flex-row justify-center items-center gap-6 mb-16">
          <img src={img1} alt="Interior Design 1" className="w-full md:w-1/3 h-[320px] rounded-lg shadow-lg object-cover" />
          <img src={img2} alt="Interior Design 2" className="w-full md:w-1/3 h-[320px] rounded-lg shadow-lg object-cover" />
          <img src={img3} alt="Interior Design 3" className="w-full md:w-1/3 h-[320px] rounded-lg shadow-lg object-cover" />
        </div>






        {/* Testimonial code */}
        {/* <div className="mb-16">
          <h2 className="text-4xl font-bold text-center mb-8">Testimonials</h2>
          <div className="flex flex-col md:flex-row justify-around items-center">
            <div className="md:w-1/3 p-4 bg-gray-800 rounded-lg shadow-lg transform hover:scale-105 transition-transform duration-300">
              <p className="italic mb-4">"Sparrow Interiors transformed our living room into a beautiful and functional space. We couldn't be happier!"</p>
              <p className="text-right">- Client A</p>
            </div>
            <div className="md:w-1/3 p-4 bg-gray-800 rounded-lg shadow-lg transform hover:scale-105 transition-transform duration-300">
              <p className="italic mb-4">"Their attention to detail and creativity is unmatched. Highly recommend!"</p>
              <p className="text-right">- Client B</p>
            </div>
          </div>
        </div> */}

        {/* <div className="mb-16">
          <h2 className="text-4xl font-bold text-center mb-8">Meet Our Team</h2>
          <div className="flex flex-col md:flex-row justify-around items-center">
            <div className="md:w-1/4 p-4 transform hover:scale-105 transition-transform duration-300">
              <img src="/path/to/team1.jpg" alt="Team Member 1" className="rounded-full shadow-xl mb-4 w-32 h-32 object-cover" />
              <h3 className="text-2xl font-bold">Alex Johnson</h3>
              <p>Lead Designer</p>
            </div>
            <div className="md:w-1/4 p-4 transform hover:scale-105 transition-transform duration-300">
              <img src="/path/to/team2.jpg" alt="Team Member 2" className="rounded-full shadow-xl mb-4 w-32 h-32 object-cover" />
              <h3 className="text-2xl font-bold">Maria Rodriguez</h3>
              <p>Project Manager</p>
            </div>
            <div className="md:w-1/4 p-4 transform hover:scale-105 transition-transform duration-300">
              <img src="/path/to/team3.jpg" alt="Team Member 3" className="rounded-full shadow-xl mb-4 w-32 h-32 object-cover" />
              <h3 className="text-2xl font-bold">James Smith</h3>
              <p>Interior Architect</p>
            </div>
          </div>
        </div> */}

        <Quote />

        <section>
          <div className="mx-auto flex w-11/12 max-w-maxContent flex-col justify-between gap-10 text-richblack-500">
            <div className="flex flex-col items-center gap-10 lg:flex-row justify-between">
              <div className="my-24 flex lg:w-[50%] flex-col gap-10">
                <h1 className="bg-gradient-to-br from-[#833AB4] via-[#FD1D1D] to-[#FCB045] bg-clip-text text-4xl font-semibold text-transparent lg:w-[70%] ">
                  Our Founding Story
                </h1>
                <p className="text-base font-medium text-richblack-300 lg:w-[95%]">
                  Sparrow Interiors began with a simple dream—to make every home a reflection of the people who live in it.

                  What started as a passion project by a group of design enthusiasts quickly grew into a full-fledged mission: to reimagine spaces with heart, purpose, and style.

                  We noticed that many homeowners struggled to find a design team that truly listened
                </p>
                <p className="text-base font-medium text-richblack-300 lg:w-[95%]">
                  We noticed that many homeowners struggled to find a design team that truly listened to their vision. That’s when we decided to be different—to blend creativity with collaboration, and luxury with livability.

                  Since day one, we’ve been committed to delivering personalized, thoughtful interiors that tell a story
                </p>
              </div>

              <div>
                <img
                  src={Story}
                  alt=""
                  className="shadow-[0_0_20px_0] shadow-[#FC6767]"
                />
              </div>
            </div>

            <CounterSection/>

            <div className="flex flex-col items-center lg:gap-10 lg:flex-row justify-between">
              <div className="my-24 flex lg:w-[40%] flex-col gap-10">
                <h1 className="bg-gradient-to-b from-[#FF512F] to-[#F09819] bg-clip-text text-4xl font-semibold text-transparent lg:w-[70%] ">
                  Our Vision
                </h1>
                <p className="text-base font-medium text-richblack-300 lg:w-[95%]">
                  At Sparrow Interiors, our vision is to transform everyday spaces into timeless expressions of beauty, comfort, and functionality.

                  We believe that great design is not just about how a space looks, but how it feels—how it flows, how it reflects your personality, and how it supports your lifestyle.

                  Our goal is to bring harmony between creativity and practicality, crafting interiors that inspire and uplift, whether it’s a cozy home, a dynamic workspace, or a luxury retreat.

                  With a commitment to quality, detail, and innovation, we aim to become a trusted name in interior design, delivering excellence that speaks for itself.
                </p>
              </div>
              <div className="my-24 flex lg:w-[40%] flex-col gap-10">
                <h1 className="bg-gradient-to-b from-[#1FA2FF] via-[#12D8FA] to-[#A6FFCB] text-transparent bg-clip-text text-4xl font-semibold lg:w-[70%] ">
                  Our Mission
                </h1>
                <p className="text-base font-medium text-richblack-300 lg:w-[95%]">
                  Our team takes over everything, from an idea and
                  concept development to realization. We believe in
                  traditions and incorporate them within our innovations. All our
                  projects incorporate a unique artistic image and functional  solutions. Client is the soul of the project. Our main goal is to  illustrate his/hers values and individuality through design.

                </p>
              </div>
            </div>
          </div>
        </section>

        <div className="text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Transform Your Space?</h2>
          <button className="bg-gradient-to-r from-blue-500 to-purple-500 text-white p-4 rounded-lg shadow-lg hover:opacity-90 transition-opacity">Contact Us Today</button>
        </div>

        <div className='mx-auto'  >
          <Form />
        </div>
      </div>
      <MapView />
      {/* Footer */}
      <Footer />

      {/* Delayed Modal Form */}
      <DelayedModalForm delayTime={4000} />

    </div>
  );
};

export default About;