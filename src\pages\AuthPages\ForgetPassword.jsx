import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { <PERSON> } from "react-router-dom";
import { BiArrowBack } from "react-icons/bi";
import Navbar from "../../Components/Common/Navbar";

// import { sendResetEmail } from "../../redux/actions/authActions";  // ← your thunk/action

const ForgotPassword = () => {
    /* ╭───────────────────────────── State ─────────────────────────────╮ */
    const [email, setEmail] = useState("");
    const [emailSent, setEmailSent] = useState(false);
    const [loading, setLoading] = useState(false);

    const dispatch = useDispatch();

    /* ╭──────────────────────────── Handler ────────────────────────────╮ */
    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            // await dispatch(sendResetEmail(email));          // ← uncomment when action exists
            // fake delay for demo
            await new Promise((res) => setTimeout(res, 800));
            setEmailSent(true);
        } catch (err) {
            console.error("Reset‑email error:", err);
            // show toast/snackbar here
        } finally {
            setLoading(false);
        }
    };

    /* ╭───────────────────────────── UI ────────────────────────────────╮ */
    return (
        <>
            <Navbar />
            <div className="grid min-h-[calc(100vh-3.5rem)] place-items-center px-4">
                {loading ? (
                    <div className="spinner" />
                ) : (
                    <div className="w-full max-w-[500px] rounded-lg bg-richblack-800 p-4 lg:p-8">
                        <h1 className="text-[1.875rem] font-semibold leading-[2.375rem] text-richblack-5">
                            {!emailSent ? "Reset your password" : "Check your email"}
                        </h1>

                        <p className="my-4 text-[1.125rem] leading-[1.625rem] text-richblack-100">
                            {!emailSent
                                ? "We'll email you instructions to reset your password. If you no longer have access to this email, please try account recovery."
                                : `We have sent password‑reset instructions to ${email}.`}
                        </p>

                        <form onSubmit={handleSubmit} className="space-y-6">
                            {!emailSent && (
                                <label className="block group relative z-0 mb-6 w-full">
                                    <span className="mb-2 block text-sm font-medium text-richblack-300 transition-all duration-200 group-focus-within:text-yellow-400">
                                        Email Address <sup className="text-pink-200">*</sup>
                                    </span>

                                    <div className="relative">
                                        <input
                                            required
                                            type="email"
                                            name="email"
                                            value={email}
                                            onChange={(e) => setEmail(e.target.value)}
                                            placeholder=" Enter your Email "
                                            className=" peer block w-full appearance-none rounded-xl border border-richblack-600 bg-richblack-800 px-4 pb-1 pt-6 text-richblack-5 
        shadow-[0_4px_10px_rgba(0,0,0,0.25)] transition-all duration-200
        focus:border-yellow-400 focus:outline-none focus:ring-2 focus:ring-yellow-500/30
        hover:border-richblack-400"
                                        />


                         
                                    </div>
                                </label>

                            )}

                            <button
                                type="submit"
                                className="w-full rounded-md bg-yellow-50 py-3 font-medium text-richblack-900 transition hover:bg-yellow-100 disabled:cursor-not-allowed disabled:opacity-50"
                                disabled={loading || (!emailSent && email.trim() === "")}
                            >
                                {!emailSent ? "Submit" : "Resend Email"}
                            </button>
                        </form>

                        <div className="mt-6 flex items-center justify-between">
                            <Link to="/login" className="inline-flex items-center gap-x-2 text-richblack-5 hover:text-richblack-50">
                                <BiArrowBack /> Back to login
                            </Link>
                        </div>
                    </div>
                )}
            </div>
        </>

    );
};

export default ForgotPassword;
