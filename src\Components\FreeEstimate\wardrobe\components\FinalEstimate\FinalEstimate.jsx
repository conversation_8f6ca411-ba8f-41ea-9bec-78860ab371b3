import React from 'react'

export const FinalEstimate = () => {
    const imga=<a href='https://d3gq2merok8n5r.cloudfront.net/stage2-1623054096-6N9Ti/wardrobe-price-calculator-1672758178-cTiiD/wardrobe-type-1672758191-cJbbM/desktop-1672758199-Y1Dwl/wardrobe-sliding-d-1672758217-GCBFE.jpg'></a>
    return (
        <div className="flex flex-col items-center justify-center    p-6">
            <h2 className="text-3xl font-semibold  text-richblack-25 font-inter  mb-6 text-center">
                Here's your estimated modular wardrobe cost
            </h2>

            <div className="bg-gray-800 rounded-xl shadow-lg max-w-md w-full overflow-hidden">
                <img
                    src={imga} // Replace with your image path
                    alt="Sliding Wardrobe"
                    className="w-full h-64 object-cover"
                />
                <div className="p-5">
                    <div className="flex justify-between items-center mb-2">
                        <h3 className="text-lg font-semibold text-richblack-50 font-inter  ">Sliding Wardrobe</h3>
                        <span className="text-green-400 text-xl font-bold">₹1.16L*</span>
                    </div>
                    <p className="text-lg font-inter text-gray-300">
                        Movable doors that slide horizontally along a metal rail and save
                        floor space.
                    </p>


                    <p className="text-xs text-gray-500 mt-4 leading-snug ">
                        *This is only an indicative price based on our clients' average
                        spends. The final price can be higher or lower depending on factors
                        like finish material, number of furniture, civil work required
                        (painting, flooring, plumbing, etc.), design elements, and wood
                        type. Don't worry, our designers can help you understand this
                        better.
                    </p>
                </div>
            </div>
        </div>
    )
}
