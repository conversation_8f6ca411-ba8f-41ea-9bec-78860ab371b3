// pages/Auth/OtpVerification.jsx
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";              // ← swap for next/link if using Next.js
import { FaArrowLeftLong, FaClockRotateLeft } from "react-icons/fa6";
import OtpInput from "react-otp-input";
import Navbar from "../../Components/Common/Navbar";
import { Footer } from "../../Components/Common/Footer";

const OtpVerification = () => {
    /* ──────────────────────── State ──────────────────────── */
    const [otp, setOtp] = useState("");
    const [timeLeft, setTimeLeft] = useState(60); // resend cooldown

    /* ───── Start countdown after mount / resend ───── */
    useEffect(() => {
        if (timeLeft === 0) return;                 // stop at zero
        const id = setInterval(() => setTimeLeft((t) => t - 1), 1000);
        return () => clearInterval(id);
    }, [timeLeft]);

    /* ─────────────────────── Handlers ────────────────────── */
    const handleSubmit = (e) => {
        e.preventDefault();
        // TODO: call verify‑OTP API
        console.log("Submitted OTP:", otp);
    };

    const handleResend = () => {
        if (timeLeft > 0) return;                  // guard if cooldown still active
        // TODO: call resend‑OTP API
        setTimeLeft(60);                           // restart cooldown
    };

    /* ───────────────────────── UI ────────────────────────── */
    return (
        <>
            <Navbar />

            <main className="flex min-h-[calc(100vh-112px)] flex-col items-center justify-center px-4 lg:px-0">
                {/* 112px ≈ Navbar+Footer height; adjust if needed */}

                <section
                    aria-labelledby="verify-heading"
                    className="w-full max-w-md rounded-lg bg-richblack-800 p-8 shadow-lg"
                >
                    <h1
                        id="verify-heading"
                        className="mb-2 text-2xl font-semibold text-richblack-5"
                    >
                        Verify Email
                    </h1>

                    <p className="mb-6 text-sm text-richblack-300">
                        We’ve sent a six‑digit verification code to your inbox. Enter it
                        below to continue.
                    </p>

                    {/* ─────────── Form ─────────── */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <OtpInput
                            value={otp}
                            onChange={setOtp}
                            numInputs={6}
                            renderInput={(props) => (
                                <input
                                    {...props}
                                    placeholder="-"
                                    style={{
                                        boxShadow: "inset 0px -1px 0px rgba(255, 255, 255, 0.18)",
                                    }}
                                    className="w-[48px] lg:w-[60px] border-0 bg-richblack-800 rounded-[0.5rem] text-richblack-5 aspect-square text-center focus:border-0 focus:outline-2 focus:outline-yellow-50"
                                />
                            )}
                            containerStyle={{
                                justifyContent: "space-between",
                                gap: "0 6px",
                            }}
                        />
                        <button
                            type="submit"
                            disabled={otp.length !== 6}
                            className="w-full rounded-md bg-yellow-50 py-2 text-center font-semibold text-richblack-900 transition hover:bg-yellow-100 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                            Verify Email
                        </button>

                        {/* Navigation & Resend */}
                        <div className="flex items-center justify-between text-sm">
                            <Link
                                to="/login"
                                className="inline-flex items-center gap-1 text-richblack-300 transition hover:text-richblack-50"
                            >
                                <FaArrowLeftLong />
                                Back to login
                            </Link>

                            <button
                                type="button"
                                onClick={handleResend}
                                disabled={timeLeft !== 0}
                                className="inline-flex items-center gap-1 text-blue-300 transition hover:text-blue-200 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                                <FaClockRotateLeft />
                                {timeLeft === 0 ? "Resend code" : `Resend in ${timeLeft}s`}
                            </button>
                        </div>
                    </form>
                </section>
            </main>

            <Footer />
        </>
    );
};

export default OtpVerification;
