

const FloatingButtons = ({ theme = 'dark' }) => {
  return (
    <div className="fixed bottom-6 right-6 z-50 flex flex-col items-end gap-6">
      {/* WhatsApp <PERSON><PERSON> */}
      <a
        href="https://wa.me/9599222158"
        className="flex items-center justify-center gap-2 bg-green-500 text-white font-medium px-4 py-3 rounded-full shadow-xl hover:bg-green-600 transition-all"
        target="_blank"
        rel="noopener noreferrer"
        style={{ width: '60px', height: '60px' }}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-7 h-7"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M12.04 2C6.56 2 2 6.53 2 12.01c0 1.93.51 3.78 1.48 5.41L2 22l4.7-1.41c1.57.86 3.35 1.32 5.34 1.32 5.48 0 10.04-4.53 10.04-10.01S17.52 2 12.04 2zm.01 18.15c-1.67 0-3.23-.48-4.57-1.3l-.33-.2-2.8.85.88-2.73-.21-.35a8.1 8.1 0 01-1.23-4.33c0-4.47 3.65-8.11 8.12-8.11 4.47 0 8.1 3.64 8.1 8.11 0 4.47-3.63 8.06-8.1 8.06zm4.34-6.01c-.23-.11-1.35-.66-1.56-.74-.21-.07-.36-.11-.51.12s-.59.74-.72.9c-.13.15-.26.17-.48.06-.23-.11-.97-.36-1.85-1.14-.68-.6-1.14-1.34-1.27-1.56-.13-.23-.01-.35.1-.46.1-.1.23-.26.34-.39.11-.13.15-.23.23-.38.08-.15.04-.29-.02-.41-.07-.11-.51-1.23-.7-1.68-.18-.43-.37-.37-.51-.38-.13 0-.29-.01-.45-.01s-.41.06-.62.29c-.21.23-.81.79-.81 1.91 0 1.12.83 2.2.95 2.35.11.15 1.63 2.48 3.95 3.48 1.52.66 2.12.72 2.88.6.46-.07 1.35-.55 1.54-1.09.19-.53.19-.99.13-1.09-.06-.1-.21-.15-.44-.26z" />
        </svg>
      </a>
    </div>
  );
};

export default FloatingButtons;
