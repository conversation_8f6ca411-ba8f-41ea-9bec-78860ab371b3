
import Slider from 'react-slick';
import { FaPaint<PERSON>oller, FaCouch, FaSmile, FaRulerCombined, FaTools, FaLightbulb, FaHome, FaUserCheck } from 'react-icons/fa';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Link } from 'react-router-dom';

export const WhyChooseUs = () => {
  const features = [
    { title: 'Expert Designers', icon: <FaPaintRoller className="text-5xl text-yellow-200 mb-4" /> },
    { title: 'Quality Materials', icon: <FaCouch className="text-5xl text-yellow-200  mb-4" /> },
    { title: 'Customer Satisfaction', icon: <FaSmile className="text-5xl text-yellow-200  mb-4" /> },
    { title: 'Space Planning', icon: <FaRulerCombined className="text-5xl text-yellow-200  mb-4" /> },
    { title: 'End-to-End Service', icon: <FaTools className="text-5xl text-yellow-200  mb-4" /> },
    { title: 'Creative Concepts', icon: <FaLightbulb className="text-5xl text-yellow-200  mb-4" /> },
    { title: 'Stylish Homes', icon: <FaHome className="text-5xl text-yellow-200 mb-4" /> },
    { title: 'Trusted Team', icon: <FaUserCheck className="text-5xl text-yellow-200 mb-4" /> },
  ];

  const settings = {
    arrows: false,
    infinite: true,
    speed: 3000, // duration of one slide movement
    slidesToShow: 5,
    slidesToScroll: 1, // scroll one at a time
    autoplay: true,
    autoplaySpeed: 0, // start sliding continuously
    cssEase: 'linear', // smooth continuous motion
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 5,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 4,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };


  return (
    <div className="px-4 py-8 overflow-x-hidden">
      <h2 className="text-3xl font-bold text-center text-richblack-200 mb-10">
        Why Choose Us?
      </h2>

      <Slider {...settings}>
        {features.map((feature, index) => (
          <div key={index} className="px-2">
            <div className="bg-gray-800 text-white p-6 rounded-lg shadow-md flex flex-col items-center justify-center text-center h-60">
              {feature.icon}
              <h3 className="text-lg font-semibold">{feature.title}</h3>
            </div>
          </div>
        ))}
      </Slider>

      <div className="mt-12 text-center">
        <h3 className="text-2xl font-semibold text-richblack-200 mb-4">
          Your Dream Home Awaits
        </h3>
        <p className="text-lg text-gray-400 mb-6">
          Let us create a space that reflects your lifestyle and taste.
        </p>
        <Link to='appointment'>
          <button className="bg-yellow-400 text-white px-6 py-3 rounded-lg hover:bg-red-500 transition duration-200">
            Get Started
          </button>
        </Link>
      </div>
    </div>
  );
};
