import { useState } from 'react'
import { Footer } from '../Components/Common/Footer'
import { MapView } from '../Components/HomePage/Map'
import Navbar from '../Components/Common/Navbar'
import Breadcrumb from '../Components/Common/Breadcrumb'
import { FaEnvelope, FaTimes } from "react-icons/fa";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
  });

  const [errors, setErrors] = useState({});

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validation
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Invalid email format";
    if (!formData.phone.trim()) newErrors.phone = "Phone number is required";

    setErrors(newErrors);

    // If no errors, submit form
    if (Object.keys(newErrors).length === 0) {
      console.log("Form Submitted:", formData);
      // Clear form
      setFormData({ name: '', email: '', phone: '' });
      setShowForm(false);
    }
  };

  const [showForm, setShowForm] = useState(false); // Missing state added
  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  return (
    <div className="bg-richblack-900 text-white min-h-screen">
      <Navbar />
      <Breadcrumb />

      {/* Hero Section */}
      <div className="py-16 px-4 lg:px-24 bg-gradient-to-br from-richblack-900 via-richblack-800 to-richblack-900">
        <h1 className="text-5xl font-bold text-center mb-4">Let's Connect</h1>
        <p className="text-center text-richblack-300 max-w-2xl mx-auto">
          Have an idea or just want to say hello? We'd love to hear from you. Fill out the form or drop by!
        </p>
      </div>

      {/* Contact & Form Section */}
      <div className="flex flex-col lg:flex-row justify-between px-4 lg:px-24 py-12 gap-12">

        {/* Left Side - Contact Info */}
        <div className="lg:w-1/2 space-y-10">
          <div>
            <h2 className="text-3xl font-semibold mb-2">📧 Chat with us</h2>
            <p className="text-richblack-300 mb-1">Our friendly team is here to help.</p>
            <p className="text-blue-400 underline">www.sparrowinterior.in</p>
          </div>

          <div>
            <h2 className="text-3xl font-semibold mb-2">📍 Visit us</h2>
            <p className="text-richblack-300 mb-1">E-212, Basement, Sector -63,
              Noida 201301
              .</p>
            <p>Thank You  </p>
            <p className="text-gray-400"></p>
          </div>

          <div>
            <h2 className="text-3xl font-semibold mb-2">📞 Call us</h2>
            <p className="text-richblack-300 mb-1">Mon - Fri From 8am to 5pm</p>
            <p className="text-blue-400 underline">9599222158
              <p>
                9311268383
              </p>

            </p>
          </div>
        </div>

        {/* Right Side - Contact Form */}
        <div className="lg:w-1/2 bg-richblack-800 p-8 rounded-2xl shadow-2xl space-y-6 animate-fade-in">
          <h2 className="text-3xl font-bold mb-2">Got an Idea? 💡 Let’s Collaborate!</h2>
          <p className="text-richblack-300">Tell us more about yourself and what you've got in mind.</p>

          <form className="space-y-5">
            <div className="flex flex-col md:flex-row gap-4">
              <input type="text" placeholder="First Name" className="w-full p-3 rounded bg-richblack-700 text-white focus:ring-2 ring-blue-500 outline-none" />
              <input type="text" placeholder="Last Name" className="w-full p-3 rounded bg-richblack-700 text-white focus:ring-2 ring-blue-500 outline-none" />
            </div>
            <input type="email" placeholder="Email Address" className="w-full p-3 rounded bg-richblack-700 text-white focus:ring-2 ring-blue-500 outline-none" />
            <input type="tel" placeholder="Phone Number" className="w-full p-3 rounded bg-richblack-700 text-white focus:ring-2 ring-blue-500 outline-none" />
            <textarea placeholder="Your Message" className="w-full p-3 rounded bg-richblack-700 text-white focus:ring-2 ring-blue-500 outline-none resize-none min-h-[120px]" />

            <button
              type="submit"
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-lg text-lg font-semibold hover:opacity-90 transition-all duration-200"
            >
              🚀 Submit
            </button>
          </form>
        </div>
      </div>
      <MapView />

      <Footer />
      {/* Floating Help Button & Form */}
      <div className="fixed bottom-6 right-6 z-50 flex flex-col items-end gap-4">
        {/* Email / Query Button or Form */}
        {showForm ? (
          <div className="bg-gray-800 p-6 w-80 shadow-2xl rounded-xl relative">
            {/* Close Button */}
            <button
              onClick={() => setShowForm(false)}
              className="absolute top-2 right-2 text-white text-xl hover:text-yellow-400"
            >
              <FaTimes />
            </button>
            <h2 className="text-2xl font-semibold text-white mb-4">Have a Query?</h2>
            <form className="space-y-4" onSubmit={handleSubmit}>
              <div>
                <label className="block text-white font-medium mb-1">Full Name</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter your name"
                  className="w-full p-3 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-400"
                />
                {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-white font-medium mb-1">Email</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Enter your email"
                  className="w-full p-3 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-400"
                />
                {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
              </div>

              <div>
                <label className="block text-white font-medium mb-1">Phone Number</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="Enter your phone number"
                  className="w-full p-3 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-400"
                />
                {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
              </div>

              <button
                type="submit"
                className="w-full bg-yellow-500 text-white font-semibold py-3 rounded-md hover:bg-yellow-400 transition"
              >
              Send Email
              </button>
            </form>
          </div>
        ) : (
          <button
            onClick={() => setShowForm(true)}
            className="bg-yellow-500 text-white p-4 rounded-full shadow-lg hover:bg-yellow-400 transition-all"
            aria-label="Open Query Form"
          >
            <FaEnvelope className="text-xl" />
          </button>
        )}


      </div>
    </div>
  )
}

export default Contact
