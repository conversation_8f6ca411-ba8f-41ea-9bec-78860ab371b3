import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import Navbar from '../Components/Common/Navbar';
import { Footer } from '../Components/Common/Footer';
import Breadcrumb from '../Components/Common/Breadcrumb';
import { FaArrowLeft, FaMapMarkerAlt, FaCalendarAlt, FaRuler, FaShare } from 'react-icons/fa';

// Import project images
import project1 from '../assets/Images/About1.webp';
import project2 from '../assets/Images/About2.webp';
import project3 from '../assets/Images/About3.webp';
import project4 from '../assets/Images/Story.png';
import project5 from '../assets/Images/furniture_selection.jpeg';
import project6 from '../assets/Images/custom_interior_design.jpeg';
import project7 from '../assets/Images/space_planning.jpeg';
import project8 from '../assets/Images/home_renovation.jpeg';

const ProjectDetail = () => {
  const { slug } = useParams();
  const [selectedImage, setSelectedImage] = useState(0);

  // Project data - matches the structure from Projects page
  const projectData = {
    'modern-luxury-villa': {
      title: 'Modern Luxury Villa',
      category: 'residential',
      location: 'Delhi NCR',
      area: '4,500 sq.ft',
      year: '2024',
      client: 'Private Residence',
      description: 'A contemporary luxury villa featuring modern design elements with traditional Indian touches.',
      challenge: 'The client wanted a modern villa that would reflect contemporary design while maintaining a connection to traditional Indian architecture. The challenge was to create a space that felt both luxurious and homely, incorporating sustainable materials and energy-efficient systems.',
      solution: 'We designed a stunning villa that seamlessly blends modern aesthetics with traditional Indian elements. The use of natural materials, open spaces, and strategic lighting creates a harmonious living environment that celebrates both comfort and style.',
      features: [
        'Open-plan living spaces',
        'Traditional Indian architectural elements',
        'Sustainable materials throughout',
        'Energy-efficient lighting systems',
        'Custom-designed furniture',
        'Landscaped gardens',
        'Smart home automation',
        'Natural ventilation systems'
      ],
      images: [project1, project2, project3, project4],
      relatedProjects: [
        { title: 'Corporate Office Design', image: project2, slug: 'corporate-office-design' },
        { title: 'Boutique Hotel Interior', image: project3, slug: 'boutique-hotel-interior' },
        { title: 'Premium Apartment', image: project4, slug: 'premium-apartment' }
      ]
    },
    'corporate-office-design': {
      title: 'Corporate Office Design',
      category: 'commercial',
      location: 'Gurgaon',
      area: '8,000 sq.ft',
      year: '2024',
      client: 'Tech Corporation',
      description: 'Innovative office space design promoting productivity and employee well-being.',
      challenge: 'The client needed a modern office space that would foster collaboration while providing quiet zones for focused work. The challenge was to create an environment that would boost productivity and employee satisfaction within a limited budget.',
      solution: 'We created a dynamic workspace with flexible zones, incorporating biophilic design elements and ergonomic furniture. The design promotes both collaboration and individual focus, with natural lighting and sustainable materials throughout.',
      features: [
        'Flexible workspace zones',
        'Biophilic design elements',
        'Ergonomic furniture',
        'Natural lighting optimization',
        'Collaborative meeting spaces',
        'Quiet focus areas',
        'Sustainable materials',
        'Modern technology integration'
      ],
      images: [project2, project1, project3, project4],
      relatedProjects: [
        { title: 'Modern Luxury Villa', image: project1, slug: 'modern-luxury-villa' },
        { title: 'Boutique Hotel Interior', image: project3, slug: 'boutique-hotel-interior' },
        { title: 'Contemporary Restaurant', image: project5, slug: 'contemporary-restaurant' }
      ]
    },
    'boutique-hotel-interior': {
      title: 'Boutique Hotel Interior',
      category: 'hospitality',
      location: 'Mumbai',
      area: '12,000 sq.ft',
      year: '2023',
      client: 'Hospitality Group',
      description: 'Elegant boutique hotel interior combining luxury with local cultural elements.',
      challenge: 'Creating a unique hospitality experience that reflects local culture while meeting international luxury standards. The challenge was to design spaces that would appeal to both business and leisure travelers.',
      solution: 'We developed a design concept that celebrates local artisans and materials while providing world-class amenities. Each space tells a story through carefully curated art, furniture, and lighting.',
      features: [
        'Local artisan collaborations',
        'Cultural design elements',
        'Luxury amenities',
        'Sustainable practices',
        'Custom lighting design',
        'Boutique atmosphere',
        'Premium materials',
        'Unique guest experiences'
      ],
      images: [project3, project1, project2, project4],
      relatedProjects: [
        { title: 'Modern Luxury Villa', image: project1, slug: 'modern-luxury-villa' },
        { title: 'Corporate Office Design', image: project2, slug: 'corporate-office-design' },
        { title: 'Premium Apartment', image: project4, slug: 'premium-apartment' }
      ]
    }
  };

  const project = projectData[slug] || projectData['modern-luxury-villa'];

  return (
    <div className="bg-richblack-900 min-h-screen">
      <Navbar />
      <Breadcrumb />

      {/* Hero Section - Full Width Image */}
      <div className="relative h-[80vh] w-full overflow-hidden">
        <img
          src={project.images[0]}
          alt={project.title}
          className="h-full w-full object-cover"
        />
      </div>

      {/* Project Title Section */}
      <div className="bg-richblack-900 py-16">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8 leading-tight">
            {project.title}
          </h1>
          <div className="flex flex-wrap justify-center gap-8 text-gray-300">
            <div className="text-center">
              <div className="text-gold-500 font-semibold mb-1">CLIENT</div>
              <div>{project.client}</div>
            </div>
            <div className="text-center">
              <div className="text-gold-500 font-semibold mb-1">LOCATION</div>
              <div>{project.location}</div>
            </div>
            <div className="text-center">
              <div className="text-gold-500 font-semibold mb-1">AREA</div>
              <div>{project.area}</div>
            </div>
            <div className="text-center">
              <div className="text-gold-500 font-semibold mb-1">YEAR</div>
              <div>{project.year}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-richblack-900">
        <div className="max-w-6xl mx-auto px-4 py-16">
          {/* Back Button */}
          <Link
            to="/projects"
            className="inline-flex items-center gap-2 text-gold-400 hover:text-gold-300 transition-colors duration-300 mb-12"
          >
            <FaArrowLeft /> Back to Portfolio
          </Link>

          {/* Project Description */}
          <div className="mb-16">
            <div className="max-w-4xl mx-auto text-center">
              <p className="text-gray-300 text-lg leading-relaxed mb-12">
                {project.description}
              </p>
            </div>
          </div>

          {/* Challenge & Solution Sections */}
          <div className="grid md:grid-cols-2 gap-16 mb-16">
            <div>
              <h3 className="text-2xl font-bold text-white mb-6">The Challenge</h3>
              <p className="text-gray-300 leading-relaxed">
                {project.challenge}
              </p>
            </div>
            <div>
              <h3 className="text-2xl font-bold text-white mb-6">Our Solution</h3>
              <p className="text-gray-300 leading-relaxed">
                {project.solution}
              </p>
            </div>
          </div>

          {/* Image Gallery */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-white mb-8 text-center">Project Gallery</h3>
            {/* Large Images Grid - Masonry Style */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {project.images.slice(1).map((image, index) => (
                <div key={index} className="group cursor-pointer overflow-hidden rounded-lg">
                  <img
                    src={image}
                    alt={`${project.title} ${index + 2}`}
                    className={`w-full object-cover transition-transform duration-700 group-hover:scale-110 ${
                      index % 2 === 0 ? 'h-96' : 'h-80'
                    }`}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Project Features */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-white mb-8 text-center">Key Features</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {project.features.map((feature, index) => (
                <div key={index} className="bg-richblack-800 p-6 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-gold-500 rounded-full mr-4 flex-shrink-0"></div>
                    <span className="text-gray-300">{feature}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Related Projects */}
      <div className="bg-richblack-800 py-16">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-white mb-12 text-center">Related Projects</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {project.relatedProjects.map((relatedProject, index) => (
              <Link
                key={index}
                to={`/projects/${relatedProject.slug}`}
                className="block group"
              >
                <div className="bg-richblack-700 rounded-lg overflow-hidden hover:bg-richblack-600 transition-all duration-500 transform hover:-translate-y-2">
                  <img
                    src={relatedProject.image}
                    alt={relatedProject.title}
                    className="w-full h-48 object-cover transform transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white group-hover:text-gold-400 transition-colors duration-300">
                      {relatedProject.title}
                    </h3>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-richblack-900 to-richblack-800 py-20">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Inspired by This Project?</h2>
          <p className="text-gray-300 text-lg mb-8 max-w-2xl mx-auto">
            Let us create something equally stunning for your space. Contact us to discuss your vision and bring your dream interior to life.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="inline-flex items-center justify-center px-8 py-4 bg-gold-500 text-richblack-900 rounded font-semibold hover:bg-gold-400 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Project
            </Link>
            <a
              href="tel:9599222158"
              className="inline-flex items-center justify-center px-8 py-4 bg-transparent border-2 border-gold-500 text-gold-500 rounded font-semibold hover:bg-gold-500 hover:text-richblack-900 transition-all duration-300"
            >
              Call: 9599222158
            </a>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default ProjectDetail;
