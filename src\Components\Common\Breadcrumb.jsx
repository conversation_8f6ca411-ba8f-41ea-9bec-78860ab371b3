
import { Link, useLocation } from 'react-router-dom';
import { FaHome, FaChevronRight } from 'react-icons/fa';

const Breadcrumb = ({ theme = 'dark', customCrumbs }) => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter((x) => x);

  // Map of path segments to display names
  const pathMap = {
    'interiors': 'Interiors',
    'kitchen': 'Kitchen',
    'wardrobe': 'Wardrobe',
    'bathroom': 'Bathroom',
    'bedroom': 'Bedroom',
    'living-room': 'Living Room',
    'home-interior': 'Home Interior',
    'about': 'About Us',
    'contact': 'Contact',
    'services': 'Services',
    'portfolio': 'Portfolio',
    'blog': 'Blog',
    'estimate': 'Get Estimate',
    'login': 'Login',
    'signup': 'Sign Up',
  };

  // If custom crumbs are provided, use those instead
  const crumbs = customCrumbs || pathnames.map((name, index) => {
    const routeTo = `/${pathnames.slice(0, index + 1).join('/')}`;
    const displayName = pathMap[name] || name.charAt(0).toUpperCase() + name.slice(1).replace(/-/g, ' ');

    return {
      path: routeTo,
      label: displayName,
      isLast: index === pathnames.length - 1
    };
  });

  // If there are no pathnames, don't render the breadcrumb
  if (pathnames.length === 0 && !customCrumbs) {
    return null;
  }

  return (
    <nav
      className={`py-2 ${
        theme === 'light'
          ? 'text-richblack-800'
          : 'text-gray-200'
      }`}
      aria-label="Breadcrumb"
    >
      <div className="container mx-auto max-w-maxContent px-4">
        <ol className="flex flex-wrap items-center text-xs md:text-sm">
          <li className="flex items-center">
            <Link
              to="/"
              className={`flex items-center hover:${
                theme === 'light' ? 'text-gold-600' : 'text-gold-400'
              } transition-colors duration-300`}
            >
              <FaHome className={`mr-1 text-xs ${
                theme === 'light' ? 'text-gold-500' : 'text-gold-400'
              }`} />
              <span>Home</span>
            </Link>
          </li>

          {crumbs.map((crumb, i) => (
            <li key={i} className="flex items-center">
              <FaChevronRight
                className={`mx-1.5 text-[10px] ${
                  theme === 'light' ? 'text-gray-400' : 'text-gray-500'
                }`}
              />
              {crumb.isLast ? (
                <span className={`font-medium ${
                  theme === 'light' ? 'text-gold-600' : 'text-gold-400'
                }`}>
                  {crumb.label}
                </span>
              ) : (
                <Link
                  to={crumb.path}
                  className={`hover:${
                    theme === 'light' ? 'text-gold-600' : 'text-gold-400'
                  } transition-colors duration-300`}
                >
                  {crumb.label}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  );
};

export default Breadcrumb;
