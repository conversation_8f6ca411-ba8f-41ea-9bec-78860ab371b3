
import { SignUpForm } from '../Auth/SignUpForm';
import { LoginForm } from '../Auth/LoginForm';
import frameImg from '../../assets/Images/frame.png';

export const Template = ({ heading, paragraph, text, form, image }) => {
  return (
    <div className='w-full h-full flex items-center justify-center pt-4 pb-80'>
      <div className='flex flex-col md:flex-row justify-between items-center w-11/12 max-w-6xl gap-8'>
        {/* Left Content - Text and Form */}
        <div className='w-full md:w-1/2 max-w-[450px]'>
          <h1 className='text-richblack-5 text-3xl font-inter font-semibold'>{heading}</h1>
          <p className='text-richblack-300 font-inter mt-2'>{paragraph}</p>
          <p className='font-inter font-semibold text-blue-300 mb-6'>{text}</p>

          {/* Conditional Form */}
          {form === 'Signup' ? <SignUpForm /> : <LoginForm />}
        </div>

        {/* Right Content - Images */}
        <div className='relative w-full md:w-1/2 max-w-[450px] hidden md:block'>
          <img
            src={frameImg}
            alt='Pattern'
            width={558}
            height={504}
            loading='lazy'
            className='w-full'
          />
          <img
            src={image}
            alt='Interior Design'
            width={558}
            height={504}
            loading='lazy'
            className='absolute h-[504px] w-[90%] top-4 right-4 z-10 object-cover'
          />
        </div>
      </div>
    </div>
  );
};
