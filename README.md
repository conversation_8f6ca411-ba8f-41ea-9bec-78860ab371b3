# Sparrow Interiors

A modern, responsive interior design company website built with React and Vite.

## 🌟 Features

- **Modern Design**: Clean, professional interface with smooth animations
- **Responsive**: Fully responsive design that works on all devices
- **Portfolio**: Beautiful project gallery with filtering capabilities
- **Services**: Comprehensive service pages with detailed information
- **Store**: Product catalog with detailed product pages
- **Contact**: Multiple contact options and forms
- **SEO Optimized**: Meta tags, sitemap, and robots.txt included

## 🚀 Live Demo

Visit the live website: [https://sparrowinteriors.vercel.app](https://sparrowinteriors.vercel.app)

## 🛠️ Tech Stack

- **Frontend**: React 18, Vite
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **Icons**: React Icons
- **Animations**: Framer Motion
- **State Management**: Redux Toolkit
- **Forms**: React Hook Form
- **Notifications**: React Hot Toast

## 📦 Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/sparrow-interiors.git
cd sparrow-interiors
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Start development server:
```bash
npm run dev
```

## 🏗️ Build for Production

```bash
npm run build
```

## 📱 Contact Information

- **Phone**: **********
- **Email**: <EMAIL>
- **Instagram**: [@sparrowinteriors.in](https://instagram.com/sparrowinteriors.in)
- **Facebook**: [/sparrowinterior](https://facebook.com/sparrowinterior)
- **Twitter**: [@SparrowInter](https://twitter.com/SparrowInter)

## 🎨 Services

- Commercial & Residential Designing
- Complete Interior Package
- Modular Kitchen & Wardrobes
- Street & Garden Landscaping
- Architects & Plan Layout
- Painting & Floor Tiling
- False Ceiling & Electrical
- Furniture & Office Infrastructure
- Turnkey & Construction Projects
- Upholstery Services

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support, email <EMAIL> or call **********.
