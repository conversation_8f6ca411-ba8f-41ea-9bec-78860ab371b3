import React from 'react';

export const TemplateEstimate = ({ items, title }) => {
    const totalEstimate = items.reduce((sum, item) => sum + item.price, 0);

    return (
        <>
            <h2 className="text-xl sm:text-2xl font-semibold text-richblack-25 mb-2 text-center">
                Here is Your Final Estimated Price
            </h2>

            <div className="max-w-lg mx-auto mt-12 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white shadow-2xl rounded-3xl overflow-hidden border border-gray-700">
                {/* Header image */}
                <div className="relative h-40 bg-cover bg-center" style={{ backgroundImage: `url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c')` }}>
                    <div className="absolute inset-0 bg-black bg-opacity-30" />
                </div>

                <div className="p-6 sm:p-8">


                    <h3 className="text-3xl font-bold mb-6 text-yellow-500 tracking-wide text-center">
                        {title || 'Estimated Design Cost'}
                    </h3>

                    <div className="space-y-3">
                        {items.map((item, index) => (
                            <div
                                key={index}
                                className="flex justify-between border-b border-gray-700 pb-2 text-gray-300 hover:text-white transition duration-200"
                            >
                                <span>{item.name}</span>
                                <span>₹{item.price.toLocaleString()}</span>
                            </div>
                        ))}
                    </div>

                    <hr className="my-6 border-gray-600" />

                    <div className="flex justify-between text-xl font-bold text-yellow-400">
                        <span>Total Estimate</span>
                        <span>₹{totalEstimate.toLocaleString()}</span>
                    </div>

                    <button className="mt-8 w-full bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-black font-semibold py-3 px-4 rounded-xl shadow-lg transition duration-300">
                        Book Free Consultation
                    </button>
                </div>
                <p className="mt-1 mb-4 text-sm text-gray-400 text-center italic">
                    <span className='text-red-500'>*Note </span>: Final pricing may vary based on design complexity and material selection.
                </p>
            </div>
        </>

    );
};
