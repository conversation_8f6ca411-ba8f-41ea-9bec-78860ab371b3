import React from 'react';

const DesignCard = ({ imgSrc, title, chatCount }) => (
  <div className="rounded-xl overflow-hidden shadow-md relative max-w-sm">
    <img src={imgSrc} alt={title} className="w-full h-52 object-cover" />
    
    {/* Heart Icon */}
    <div className="absolute top-3 right-3 bg-white p-2 rounded-full shadow">
      ❤️
    </div>

    {/* Chat Count Icon */}
    <div className="absolute bottom-3 right-3 bg-white px-2 py-1 rounded-full shadow text-sm flex items-center gap-1">
      💬 <span>{chatCount}</span>
    </div>

    <div className="p-4">
      <h3 className="text-base font-semibold">{title}</h3>
      <div className="flex gap-3 mt-3">
        <button className="border border-red-400 text-red-500 px-3 py-1 rounded-full text-sm hover:bg-red-50">
          Book Free Consultation
        </button>
        <button className="bg-red-500 text-white px-4 py-1 rounded-full text-sm">
          Get Quote
        </button>
      </div>
    </div>
  </div>
);

export default DesignCard;