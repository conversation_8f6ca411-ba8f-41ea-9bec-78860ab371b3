import React, { useState } from 'react'
import { Footer } from '../../Common/Footer'
import { Link } from 'react-router-dom';
import Navbar from '../../Common/Navbar';;
const HeightSelection = () => {
  const [selected, setSelected] = useState('7 ft');
  const options = ['4 ft', '6 ft', '7 ft', '9 ft'];
  return (
    <div>
      <Navbar/>
      <div className=" mb-5 bg-richblack-900 flex items-center justify-center px-4">
        <div className="max-w-xl w-full bg-richblack-800 text-richblack-25 rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold mb-2 text-center text-richblack-25 font-inter">What is the height of your wardrobe?</h2>


          <div className="bg-richblack-500 text-richblack-50 font-inter font-semibold  px-4 py-2 rounded text-center text-sm mb-6">
            Standard size has been set for your convenience
          </div>

          <div className="space-y-3">
            {options.map((option) => (
              <div
                key={option}
                className={`flex items-center justify-between p-4 rounded-lg border cursor-pointer transition-all duration-300 ${selected === option
                  ? 'bg-yellow-100 text-richblack-900 border-yellow-200'
                  : 'bg-richblack-700 border-richblack-600'
                  }`}
                onClick={() => setSelected(option)}
              >
                <span>{option}</span>
                <div className={`w-5 h-5 rounded-full border-2 ${selected === option ? 'border-richblack-900 bg-richblack-900' : 'border-richblack-300'
                  }`}></div>
              </div>
            ))}
          </div>

          <div className="flex justify-between items-center mt-10">
            <button className="text-yellow-300 hover:text-yellow-300 font-medium transition-all">Back</button>
            <Link to='types'>
            <button className="bg-yellow-300 hover:bg-yellow-300 text-richblack-900 font-semibold px-6 py-2 rounded-full transition-all">
              Next
            </button>
            </Link>
          </div>
        </div>
      </div>


      <Footer />



    </div>
  )
}

export default HeightSelection