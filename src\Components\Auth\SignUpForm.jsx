import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FaEye, FaEyeSlash } from 'react-icons/fa6';

export const SignUpForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit
  } = useForm();

  const submitHandler = async (data) => {
    console.log("Form Data Submitted:", data);
  };

  return (
    <div>
      <form onSubmit={handleSubmit(submitHandler)}>
        <div className='flex flex-col gap-y-4'>

          {/* First Name */}
          <div className='flex flex-col'>
            <label className='text-left text-richblack-5'>
              First Name<sup className='text-yellow-300'>*</sup>
            </label>
            <input
              className='px-2 py-2 text-white rounded-md shadow-richblack-500 shadow-sm outline-none bg-richblack-800'
              type='text'
              placeholder='Enter first name'
              {...register("firstName", { required: true })}
              required
            />
          </div>

          {/* Email */}
          <div className='flex flex-col'>
            <label className='text-left text-richblack-5'>
              Email Address<sup className='text-yellow-300'>*</sup>
            </label>
            <input
              className='py-2 px-2 text-white rounded-md outline-none shadow-richblack-500 shadow-sm bg-richblack-800'
              type='email'
              placeholder='<EMAIL>'
              {...register("email", { required: true })}
              required
            />
          </div>

          {/* Phone Number */}
          <div className='flex flex-col'>
            <label className='text-left text-richblack-5'>
              Phone Number<sup className='text-yellow-300'>*</sup>
            </label>
            <input
              className='px-2 py-2 text-white rounded-md shadow-richblack-500 shadow-sm outline-none bg-richblack-800'
              type='tel'
              placeholder='Enter phone number'
              {...register("phone", { required: true })}
              required
            />
          </div>

 
          {/* Passwords */}
          <div className='flex flex-col md:flex-row gap-x-4'>

            {/* Create Password */}
            <div className='relative w-full'>
              <label className='text-left text-richblack-5'>
                Create Password<sup className='text-yellow-300'>*</sup>
              </label>
              <input
                className='w-full px-2 py-2 text-white rounded-md shadow-richblack-500 shadow-sm outline-none bg-richblack-800'
                type={showPassword ? 'text' : 'password'}
                placeholder='Enter Password'
                {...register("password", { required: true })}
                required
              />
              <div
                className='absolute right-3 top-[38px] cursor-pointer text-white'
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </div>
            </div>

            {/* Confirm Password */}
            <div className='relative w-full'>
              <label className='text-left text-richblack-5'>
                Confirm Password<sup className='text-yellow-300'>*</sup>
              </label>
              <input
                className='w-full px-2 py-2 text-white rounded-md shadow-richblack-500 shadow-sm outline-none bg-richblack-800'
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder='Confirm Password'
                {...register("confirmPassword", { required: true })}
                required
              />
              <div
                className='absolute right-3 top-[38px] cursor-pointer text-white'
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type='submit'
            className='px-36 py-2 rounded-md font-semibold text-black bg-yellow-50'
          >
            Create Account
          </button>

        </div>
      </form>
    </div>
  );
};
