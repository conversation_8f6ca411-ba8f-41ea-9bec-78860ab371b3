import { createSlice } from '@reduxjs/toolkit'

const initialState = {
    theme: "dark",
}

const themeSlice = createSlice({
    name: 'theme',
    initialState,
    reducers: {
        setTheme: (state, action) => {
            state.theme = action.payload;
            // Save to localStorage if available
            if (typeof window !== 'undefined') {
                localStorage.setItem("theme", action.payload);
            }
        }
    }
})

export const {setTheme } = themeSlice.actions;
export default themeSlice.reducer;
