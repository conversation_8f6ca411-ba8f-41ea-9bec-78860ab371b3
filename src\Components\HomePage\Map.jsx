
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaInstagram, FaFacebookF, FaTwitter, FaLinkedinIn } from 'react-icons/fa';

export const MapView = () => {
    // Contact information
    const contactInfo = {
        address: 'E-212, Basement, Sector-63, Noida 201301, Uttar Pradesh, India',
        phone: '+91 98765 43210',
        email: '<EMAIL>',
        hours: [
            { days: 'Monday - Friday', time: '9:00 AM - 6:00 PM' },
            { days: 'Saturday', time: '10:00 AM - 4:00 PM' },
            { days: 'Sunday', time: 'Closed' }
        ],
        social: [
            { icon: <FaInstagram />, url: 'https://instagram.com' },
            { icon: <FaFacebookF />, url: 'https://facebook.com' },
            { icon: <FaTwitter />, url: 'https://twitter.com' },
            { icon: <FaLinkedinIn />, url: 'https://linkedin.com' }
        ]
    };

    return (
        <section className="py-16 bg-richblack-900 text-white">
            <div className="max-w-6xl mx-auto px-4">
                {/* Section Header */}
                <div className="mb-12 text-center">
                    <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center">
                        <span className="w-10 h-1 bg-gold-500 mr-4 inline-block"></span>
                        Visit Our Studio
                        <span className="w-10 h-1 bg-gold-500 ml-4 inline-block"></span>
                    </h2>
                    <p className="text-gray-300 max-w-3xl mx-auto">
                        Experience our design expertise firsthand by visiting our studio. Schedule a consultation to discuss your interior design project.
                    </p>
                </div>

                {/* Map and Contact Info Container */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-stretch">
                    {/* Contact Information */}
                    <div className="bg-richblack-800 rounded-lg p-8 shadow-xl order-2 lg:order-1">
                        <div className="space-y-6">
                            {/* Address */}
                            <div className="flex items-start gap-4">
                                <div className="bg-gold-500/10 p-3 rounded-full flex-shrink-0">
                                    <FaMapMarkerAlt className="text-gold-500 text-xl" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-white mb-2">Our Location</h3>
                                    <p className="text-gray-300">{contactInfo.address}</p>
                                </div>
                            </div>

                            {/* Phone */}
                            <div className="flex items-start gap-4">
                                <div className="bg-gold-500/10 p-3 rounded-full flex-shrink-0">
                                    <FaPhone className="text-gold-500 text-xl transform rotate-180" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-white mb-2">Phone Number</h3>
                                    <p className="text-gray-300">{contactInfo.phone}</p>
                                </div>
                            </div>

                            {/* Email */}
                            <div className="flex items-start gap-4">
                                <div className="bg-gold-500/10 p-3 rounded-full flex-shrink-0">
                                    <FaEnvelope className="text-gold-500 text-xl" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-white mb-2">Email Address</h3>
                                    <p className="text-gray-300">{contactInfo.email}</p>
                                </div>
                            </div>

                            {/* Hours */}
                            <div className="flex items-start gap-4">
                                <div className="bg-gold-500/10 p-3 rounded-full flex-shrink-0">
                                    <FaClock className="text-gold-500 text-xl" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-white mb-2">Working Hours</h3>
                                    <div className="space-y-1">
                                        {contactInfo.hours.map((item, index) => (
                                            <div key={index} className="text-gray-300">
                                                <span className="font-medium">{item.days}:</span> {item.time}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* Social Media */}
                            <div className="pt-6 border-t border-richblack-700">
                                <h3 className="text-lg font-semibold text-white mb-4">Follow Us</h3>
                                <div className="flex gap-4">
                                    {contactInfo.social.map((item, index) => (
                                        <a
                                            key={index}
                                            href={item.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="bg-richblack-700 hover:bg-gold-500 text-white hover:text-richblack-900 p-3 rounded-full transition-all duration-300"
                                            aria-label={`Visit our social media`}
                                        >
                                            {item.icon}
                                        </a>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Map */}
                    <div className="lg:col-span-2 order-1 lg:order-2">
                        <div className="bg-richblack-800 rounded-lg overflow-hidden shadow-xl h-full">
                            {/* Map Header */}
                            <div className="bg-richblack-700 p-4 flex items-center">
                                <div className="bg-gold-500 p-2 rounded-full mr-3">
                                    <FaMapMarkerAlt className="text-richblack-900" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-white">Our Studio Location</h3>
                                    <p className="text-sm text-gray-300">E-212, Basement, Sector-63, Noida</p>
                                </div>
                            </div>

                            {/* Map Iframe */}
                            <div className="relative w-full h-[400px] lg:h-[500px]">
                                <iframe
                                    title="Google Map"
                                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3502.0168431464513!2d77.37946867548652!3d28.627536175685837!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x390ce5456d1e0001%3A0xb5ef8a1e9cfc31a2!2sSector%2063%2C%20Noida%2C%20Uttar%20Pradesh%20201301!5e0!3m2!1sen!2sin!4v1682345678901!5m2!1sen!2sin"
                                    width="100%"
                                    height="100%"
                                    style={{ border: 0 }}
                                    allowFullScreen
                                    loading="lazy"
                                    referrerPolicy="no-referrer-when-downgrade"
                                    className="absolute inset-0"
                                />

                                {/* Map Overlay for Style */}
                                <div className="absolute inset-0 pointer-events-none border border-richblack-700"></div>
                            </div>

                            {/* Map Footer */}
                            <div className="p-4 bg-richblack-700 flex justify-between items-center">
                                <p className="text-sm text-gray-300">© 2023 Interior Design Studio</p>
                                <a
                                    href="https://maps.google.com/maps?q=E-212,+Basement,+Sector+-63,+Noida+201301"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-gold-500 hover:text-gold-400 text-sm font-medium transition-colors duration-300"
                                >
                                    Get Directions
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                {/* CTA Button */}
                <div className="mt-12 text-center">
                    <a
                        href="#contact"
                        className="inline-flex items-center gap-2 px-8 py-3 bg-gold-500 hover:bg-gold-400 text-richblack-900 font-medium rounded-sm transition-all duration-300"
                    >
                        Schedule a Visit
                    </a>
                </div>
            </div>
        </section>
    );
};
