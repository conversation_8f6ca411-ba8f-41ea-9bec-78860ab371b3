import React, { useState } from 'react'
import { Template } from './Template';
import { Link, useNavigate } from 'react-router-dom';

export const PackageDetails = ({path}) => {
    const navigate = useNavigate();
    const essentialsPackage = {
        title: "Essentials",
        currency: "₹",
        description: "A range of essential home interior solutions that's perfect for all your needs.",
        image: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c", // example image
        features: [
            "Affordable pricing",
            "Convenient designs",
            "Basic accessories"
        ],
        items: [
            { name: "sofa", label: "Sofa Set", price: 5000 },
            { name: "dining", label: "Dining Table", price: 3000 },
            { name: "curtains", label: "Curtains", price: 2000 }
        ]
    };

    const premiumPackage = {
        title: "Premium",
        currency: "₹",
        description: "A premium range with elevated style and comfort to match your modern lifestyle.",
        image: "https://images.unsplash.com/photo-1615874959474-d2d259b1a3ae", // example image
        features: [
            "Premium materials",
            "Modern designs",
            "Additional accessories"
        ],
        items: [
            { name: "sofa", label: "Designer Sofa", price: 12000 },
            { name: "dining", label: "Premium Dining Table", price: 8000 },
            { name: "wardrobe", label: "Modular Wardrobe", price: 10000 }
        ]
    };

    const luxuryPackage = {
        title: "Luxury",
        currency: "₹",
        description: "Luxury interiors with bespoke elements for a sophisticated and lavish living experience.",
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7", // example image
        features: [
            "Bespoke designs",
            "Imported materials",
            "Smart home integration"
        ],
        items: [
            { name: "sofa", label: "Italian Leather Sofa", price: 25000 },
            { name: "dining", label: "Glass Top Dining Set", price: 15000 },
            { name: "automation", label: "Smart Automation Setup", price: 20000 }
        ]
    };




    return (
        <>
            <h2 className="text-2xl font-semibold text-center text-richblack-25 font-inter mt-3 mb-6">
                Pick your package
            </h2>

            <div className='flex justify-center overflow-x-hidden  overflow-y-hidden'>
                <Template {...essentialsPackage} />
                <Template {...premiumPackage} />
                <Template {...luxuryPackage} />

            </div>

            <div className="flex justify-center mt-6 space-x-5 mb-4">
                <button
                    onClick={() => navigate(-1)}
                    className="px-4 py-2 text-yellow-300 rounded disabled:opacity-50"
                >
                    Back
                </button>

                <Link to={path} >
                    <button
                        className="px-4 py-2 bg-yellow-300 text-white rounded disabled:opacity-50"
                    >
                        Next
                    </button>
                </Link>
            </div>
        </>
    );
};
