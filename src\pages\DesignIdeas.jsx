import { useState } from 'react';
import Navbar from '../Components/Common/Navbar';
import { Footer } from '../Components/Common/Footer';
import DesignCard from '../Components/Common/DesignCard';
import { FaSearch, FaFilter } from 'react-icons/fa';

// Import dummy images
import dummyImage1 from '../assets/Images/About1.webp';
import dummyImage2 from '../assets/Images/About2.webp';
import dummyImage3 from '../assets/Images/About3.webp';
import dummyImage4 from '../assets/Images/Story.png';

// Sample design data
const designData = [
  {
    id: 1,
    image: dummyImage1,
    title: 'Modern Living Room Design',
    description: 'Elegant and contemporary living room with minimalist furniture and neutral color palette.',
    category: 'Living Room',
    price: '2,50,000',
    link: '/design/1'
  },
  {
    id: 2,
    image: dummyImage2,
    title: 'Luxurious Master Bedroom',
    description: 'Spacious master bedroom with premium finishes and custom lighting solutions.',
    category: 'Bedroom',
    price: '1,80,000',
    link: '/design/2'
  },
  {
    id: 3,
    image: dummyImage3,
    title: 'Contemporary Kitchen Design',
    description: 'Functional kitchen with modern appliances and elegant cabinetry for the perfect cooking experience.',
    category: 'Kitchen',
    price: '3,20,000',
    link: '/design/3'
  },
  {
    id: 4,
    image: dummyImage4,
    title: 'Minimalist Home Office',
    description: 'Productive workspace with clean lines and ergonomic furniture for maximum efficiency.',
    category: 'Office',
    price: '1,20,000',
    link: '/design/4'
  },
  {
    id: 5,
    image: dummyImage1,
    title: 'Elegant Dining Room',
    description: 'Sophisticated dining area with custom table and ambient lighting for perfect dinner parties.',
    category: 'Dining',
    price: '1,50,000',
    link: '/design/5'
  },
  {
    id: 6,
    image: dummyImage2,
    title: 'Stylish Bathroom Renovation',
    description: 'Luxury bathroom with premium fixtures and custom tilework for a spa-like experience.',
    category: 'Bathroom',
    price: '2,10,000',
    link: '/design/6'
  },
  {
    id: 7,
    image: dummyImage3,
    title: 'Kids Room Design',
    description: 'Playful and functional children\'s room with smart storage solutions and vibrant accents.',
    category: 'Kids Room',
    price: '1,40,000',
    link: '/design/7'
  },
  {
    id: 8,
    image: dummyImage4,
    title: 'Modern Wardrobe Solutions',
    description: 'Custom wardrobe designs with optimized storage and elegant finishes for your bedroom.',
    category: 'Wardrobe',
    price: '1,80,000',
    link: '/design/8'
  }
];

const DesignIdeas = () => {
  const [favorites, setFavorites] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('All');
  const [theme] = useState('dark'); // You can connect this to your theme context

  const toggleFavorite = (id) => {
    if (favorites.includes(id)) {
      setFavorites(favorites.filter(favId => favId !== id));
    } else {
      setFavorites([...favorites, id]);
    }
  };

  const categories = ['All', 'Living Room', 'Bedroom', 'Kitchen', 'Bathroom', 'Dining', 'Office', 'Kids Room', 'Wardrobe'];

  const filteredDesigns = designData.filter(design => {
    const matchesSearch = design.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          design.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          design.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = activeFilter === 'All' || design.category === activeFilter;
    
    return matchesSearch && matchesFilter;
  });

  return (
    <div className={theme === 'light' ? 'bg-gray-50' : 'bg-richblack-900'}>
      <Navbar />
      
      {/* Hero Section */}
      <div className="relative h-[300px] w-full overflow-hidden">
        <div className="absolute inset-0 bg-black/60 z-10"></div>
        <img 
          src={dummyImage1} 
          alt="Interior Design Ideas" 
          className="h-full w-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4">
          <h1 className="text-4xl md:text-5xl font-inter font-bold text-white mb-4">
            Interior Design Ideas
          </h1>
          <p className="text-lg text-gray-200 max-w-2xl">
            Explore our curated collection of stunning interior designs to inspire your next home transformation
          </p>
        </div>
      </div>
      
      {/* Search and Filter Section */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-8">
          {/* Search Bar */}
          <div className={`relative w-full md:w-96 ${
            theme === 'light' ? 'text-richblack-900' : 'text-white'
          }`}>
            <input
              type="text"
              placeholder="Search designs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full py-3 pl-4 pr-12 rounded-sm border ${
                theme === 'light' 
                  ? 'bg-white border-gray-300 focus:border-gold-400' 
                  : 'bg-richblack-800 border-richblack-700 focus:border-gold-500'
              } focus:outline-none focus:ring-1 focus:ring-gold-400`}
            />
            <FaSearch className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400" />
          </div>
          
          {/* Category Filters */}
          <div className="flex items-center gap-2 overflow-x-auto pb-2 w-full md:w-auto">
            <FaFilter className={`mr-2 ${theme === 'light' ? 'text-richblack-900' : 'text-white'}`} />
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setActiveFilter(category)}
                className={`whitespace-nowrap px-4 py-2 text-sm font-medium rounded-sm transition-colors duration-300 ${
                  activeFilter === category
                    ? theme === 'light'
                      ? 'bg-gold-400 text-richblack-900'
                      : 'bg-gold-500 text-richblack-900'
                    : theme === 'light'
                      ? 'bg-white text-richblack-900 border border-gray-300 hover:bg-gray-100'
                      : 'bg-richblack-800 text-white border border-richblack-700 hover:bg-richblack-700'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
        
        {/* Design Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {filteredDesigns.map(design => (
            <DesignCard
              key={design.id}
              image={design.image}
              title={design.title}
              description={design.description}
              category={design.category}
              price={design.price}
              link={design.link}
              isFavorite={favorites.includes(design.id)}
              onFavoriteToggle={() => toggleFavorite(design.id)}
              theme={theme}
            />
          ))}
        </div>
        
        {filteredDesigns.length === 0 && (
          <div className={`text-center py-12 ${
            theme === 'light' ? 'text-richblack-900' : 'text-white'
          }`}>
            <h3 className="text-2xl font-inter font-semibold mb-2">No designs found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>
      
      <Footer />
    </div>
  );
};

export default DesignIdeas;
