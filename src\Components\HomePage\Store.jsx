
import { Link } from 'react-router-dom';
import { FaStar, FaArrowRight } from 'react-icons/fa';

// Featured store items for home page
export const featuredStoreItems = [
  {
    id: 1,
    name: "Modern Scandinavian Sofa",
    description: "Elegant 3-seater sofa with premium fabric upholstery and solid wood legs.",
    rating: 4.8,
    reviewCount: 124,
    image: "https://images.unsplash.com/photo-1519710164239-da123dc03ef4?auto=format&fit=crop&w=600&q=80",
    slug: "modern-scandinavian-sofa"
  },
  {
    id: 2,
    name: "Geometric Brass Pendant Light",
    description: "Modern geometric pendant light with brass finish, perfect for dining areas.",
    rating: 4.7,
    reviewCount: 86,
    image: "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=600&q=80",
    slug: "geometric-brass-pendant-light"
  },
  {
    id: 3,
    name: "Abstract Canvas Art Set",
    description: "Set of 3 abstract canvas prints with gold accents, perfect for modern interiors.",
    rating: 4.5,
    reviewCount: 52,
    image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80",
    slug: "abstract-canvas-art-set"
  },
  {
    id: 4,
    name: "Marble Coffee Table",
    description: "Elegant coffee table with genuine marble top and gold-finished metal base.",
    rating: 4.8,
    reviewCount: 31,
    image: "https://images.unsplash.com/photo-1512820790803-83ca734da794?auto=format&fit=crop&w=600&q=80",
    slug: "marble-coffee-table"
  }
];

const OurStoreSection = () => {
  // Render star rating
  const renderRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);

    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<FaStar key={i} className="text-gold-500" />);
      } else {
        stars.push(<FaStar key={i} className="text-gray-600" />);
      }
    }

    return <div className="flex">{stars}</div>;
  };

  return (
    <section className="py-16 bg-richblack-900 text-white">
      <div className="max-w-6xl mx-auto px-4">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center">
            <span className="w-10 h-1 bg-gold-500 mr-4 inline-block"></span>
            Featured Products
            <span className="w-10 h-1 bg-gold-500 ml-4 inline-block"></span>
          </h2>
          <p className="text-gray-300 max-w-3xl mx-auto">
            Discover our curated collection of premium furniture, lighting, and decor pieces to elevate your living spaces with style and elegance.
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {featuredStoreItems.map((item) => (
            <Link
              key={item.id}
              to={`/store/product/${item.slug}`}
              className="bg-richblack-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group border border-richblack-700 hover:border-gold-500 block"
            >
              <div className="relative overflow-hidden">
                {/* Product Image */}
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-64 object-cover transform transition-transform duration-500 group-hover:scale-105"
                />

                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center p-4">
                  <div className="bg-gold-500 text-richblack-900 px-4 py-2 rounded-sm font-medium">
                    View Details
                  </div>
                </div>
              </div>

              <div className="p-5">
                {/* Product Name */}
                <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-gold-400 transition-colors duration-300">
                  {item.name}
                </h3>

                {/* Rating */}
                <div className="flex items-center gap-2 mb-4">
                  {renderRating(item.rating)}
                  <span className="text-xs text-gray-400">({item.reviewCount})</span>
                </div>

                {/* Description */}
                <p className="text-gray-300 text-sm">
                  {item.description}
                </p>
              </div>
            </Link>
          ))}
        </div>

        {/* View All Products Button */}
        <div className="text-center">
          <Link
            to="/store"
            className="inline-flex items-center gap-2 px-8 py-3 bg-gold-500 hover:bg-gold-400 text-richblack-900 font-medium rounded-sm transition-all duration-300"
          >
            Explore All Products <FaArrowRight className="text-xs" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default OurStoreSection;
