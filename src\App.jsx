import { Route, Routes } from 'react-router-dom'
import { Home } from './pages/Home'
import { SignUp } from './pages/AuthPages/SignUp'
import { Login } from './pages/AuthPages/Login'
import About from './pages/About'
import Contact from './pages/ContactUs'
import Services from './pages/Services'
import CommercialResidential from './pages/Services/CommercialResidential'
import CompleteInterior from './pages/Services/CompleteInterior'
import ModularKitchenWardrobes from './pages/Services/ModularKitchenWardrobes'
import Landscaping from './pages/Services/Landscaping'
import Projects from './pages/Projects'
import ProjectDetail from './pages/ProjectDetail'
import ProductDetail from './pages/ProductDetail'

import StorePage from './pages/StorePage'
import './App.css'

import Appointment from './Components/HomePage/Appointment'
import { FreeEstimated } from './pages/FreeEstimated'
import Otp_Verification from './pages/AuthPages/Otp_Verification'

import FloatingButtons from './Components/Common/FloatingButtons'
import DesignIdeas from './pages/DesignIdeas'
import Gallery from './pages/Gallery'
import ForgetPassword from './pages/AuthPages/ForgetPassword'
import UpdatePassword from './pages/AuthPages/UpdatePassword'

function App() {
  return (
    <div className="font-inter bg-gradient-to-b from-gray-900 to-richblack-900">
      <Routes>
        {/* Public Routes */}
        <Route index element={<Home />} />

        

        {/* AUTH routes */}
        <Route path='/signup' element={<SignUp />} />
        <Route path='/login' element={<Login />} />
        <Route path="/Otp-Verfication" element={<Otp_Verification/>} />
        <Route path="/forgot-password" element={<ForgetPassword/>} />
        <Route path="/update-password" element={<UpdatePassword/>} />

       
       
       
        {/* Other routes */}
     

     {/* Navbar */}
        <Route path="/about" element={<About />} />
        <Route path="/services" element={<Services />} />
        <Route path="/services/commercial-residential" element={<CommercialResidential />} />
        <Route path="/services/complete-interior" element={<CompleteInterior />} />
        <Route path="/services/modular-kitchen-wardrobes" element={<ModularKitchenWardrobes />} />
        <Route path="/services/landscaping" element={<Landscaping />} />
        <Route path="/projects" element={<Projects />} />
        <Route path="/projects/:slug" element={<ProjectDetail />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/store" element={<StorePage />} />
        <Route path="/store/product/:slug" element={<ProductDetail />} />
     
     

     
     {/* Other Routes
      */}
        <Route path="/appointment" element={<Appointment/>} />
        <Route path="/estimate" element={<FreeEstimated/>} />
        <Route path="/gallery" element={<Projects/>} />
        <Route path="/gallery/:subcategory" element={<Projects/>} />
        <Route path="/design-ideas" element={<DesignIdeas/>} />
     


      </Routes>

      {/* Floating buttons - appear on all pages */}
      <FloatingButtons />


    </div>
  )
}

export default App
