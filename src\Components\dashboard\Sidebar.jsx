import { interiorSidebarLinks } from '../../data/Sidebardata'
import { useLocation, Link, matchPath } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { FaHome, FaPalette } from "react-icons/fa"

export const Sidebar = () => {
    const location = useLocation()
    const { user } = useSelector((state) => state.Profile)

    const pathmatch = (route) => {
        return matchPath({ path: route }, location.pathname)
    }

    return (
        <div>
            <div className='flex flex-col min-w-[222px] bg-neutral-100 border-r-[1px] py-10 h-[100vh]'>
                <div className="px-4 mb-6">
                    <h2 className="text-2xl font-serif text-emerald-800">Design Studio</h2>
                </div>

                {interiorSidebarLinks.map((link) => (
                    <Link to={link.path} key={link.name}>
                        <div className={`flex items-center gap-3 px-4 py-3 ${pathmatch(link.path)
                                ? 'text-emerald-800 bg-emerald-50 border-l-4 border-emerald-600'
                                : 'text-gray-700 hover:bg-neutral-200'
                            }`}>
                            <span>{link.icon}</span>
                            <span className="text-lg">{link.name}</span>
                        </div>
                    </Link>
                ))}

                {user?.role === "designer" && (
                    <div className="mt-6 px-4">
                        <h3 className="text-sm uppercase text-gray-500 mb-2">Designer Tools</h3>
                        <Link to="/projects/create">
                            <div className={`flex items-center gap-3 px-4 py-3 ${pathmatch('/projects/create')
                                    ? 'text-emerald-800 bg-emerald-50 border-l-4 border-emerald-600'
                                    : 'text-gray-700 hover:bg-neutral-200'
                                }`}>
                                <span><FaPalette /></span>
                                <span className="text-lg">Create Project</span>
                            </div>
                        </Link>
                    </div>
                )}
            </div>
        </div>
    )
}