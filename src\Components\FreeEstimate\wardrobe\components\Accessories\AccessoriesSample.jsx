import React, { useState } from 'react'
import { Link } from 'react-router-dom';
const accessories = [
    { name: 'Loft', image: '/images/loft.jpg' },
    { name: 'Single full-size drawer', image: '/images/full-size.jpg' },
    { name: '1 half-drawer', image: '/images/1-half.jpg' },
    { name: '2 half-drawers', image: '/images/2-half.jpg' },
    { name: 'Wicker pull out', image: '/images/wicker.jpg' },
];

const AccessoriesSample = () => {
    const [selectedAccessories, setSelectedAccessories] = useState([]);

    const toggleAccessory = (name) => {
        setSelectedAccessories((prev) =>
            prev.includes(name) ? prev.filter((item) => item !== name) : [...prev, name]
        );
    };
    return (
        <div className="  px-4 py-7 flex justify-center items-center">
            <div className="w-full max-w-5xl bg-gray-800 rounded-3xl shadow-2xl p-8">
                <h2 className="text-3xl font-bold text-richblack-25 text-center mb-10">Add your preferred accessories (optional)</h2>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                    {accessories.map((acc, idx) => (
                        <label
                            key={idx}
                            className={`group relative cursor-pointer rounded-2xl overflow-hidden transition-all duration-300 border p-3 ${selectedAccessories.includes(acc.name)
                                ? 'border-yellow-300 shadow-lg shadow-yellow-300/30 scale-[1.02]'
                                : 'border-gray-700 hover:shadow-md hover:scale-[1.01]'
                                }`}
                            onClick={() => toggleAccessory(acc.name)}
                        >
                            <img
                                src={acc.image}
                                alt={acc.name}
                                className="rounded-xl w-full h-40 object-cover mb-3"
                            />
                            <div className="flex justify-between items-center">
                                <span className="text-white font-medium text-lg">{acc.name}</span>
                                <input
                                    type="checkbox"
                                    checked={selectedAccessories.includes(acc.name)}
                                    onChange={() => toggleAccessory(acc.name)}
                                    className="accent-yellow-300 w-5 h-5 cursor-pointer"
                                />
                            </div>
                        </label>
                    ))}
                </div>

                <div className="mt-12 flex justify-between items-center border-t border-gray-700 pt-6">
                    <button className="text-yellow-300 font-semibold hover:underline">BACK</button>

                    <Link to='estimated'>
                        <button
                            className={`px-8 py-3 rounded-full text-white font-bold transition-all tracking-wide ${selectedAccessories.length
                                ? 'bg-yellow-300 yellow-300 shadow-md shadow-yellow-300/20'
                                : 'bg-gray-600 cursor-not-allowed'
                                }`}
                            disabled={selectedAccessories.length === 0}
                        >
                            NEXT
                        </button>
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default AccessoriesSample