import { useEffect, useState } from 'react';

// Stats data
const stats = [
  { label: 'Happy Clients', value: 500, suffix: '+' },
  { label: 'Projects Completed', value: 150, suffix: '+' },
  { label: 'Interior Experts', value: 25, suffix: '+' },
  { label: 'Design Awards', value: 12, suffix: '+' },
];

// Counter Component
const Counter = ({ end, suffix, delay = 0 }) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let start = 0;
    const duration = 2000;
    const incrementTime = 50;
    const increment = end / (duration / incrementTime);

    const timeout = setTimeout(() => {
      const timer = setInterval(() => {
        start += increment;
        if (start >= end) {
          start = end;
          clearInterval(timer);
        }
        setCount(Math.floor(start));
      }, incrementTime);

      return () => clearInterval(timer);
    }, delay);

    return () => clearTimeout(timeout);
  }, [end, delay]);

  return (
    <span>
      {count}
      {suffix}
    </span>
  );
};

// Counter Section Component
const CounterSection = () => {
  return (
    <section className="bg-gradient-to-r from-richblack-900 to-richblack-800 py-10 relative">
      {/* Top gold accent line */}
      <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-gold-700 via-gold-300 to-gold-700"></div>

      <div className="max-w-6xl mx-auto px-4 text-center">
        <h2 className="text-2xl md:text-3xl font-inter font-medium text-white mb-8 uppercase tracking-wider">
          <span className="text-gold-300">Our</span> Achievements
        </h2>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="flex flex-col items-center p-4 relative group">
              {/* Gold accent for each stat */}
              <div className="absolute top-0 left-1/2 -translate-x-1/2 w-10 h-[2px] bg-gold-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <div className="text-3xl md:text-4xl font-bold text-gold-300">
                <Counter end={stat.value} suffix={stat.suffix} delay={index * 200} />
              </div>

              <p className="mt-2 text-sm uppercase tracking-wider font-medium text-white">{stat.label}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom gold accent line */}
      <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-gold-700 via-gold-300 to-gold-700"></div>
    </section>
  );
};

export default CounterSection;
