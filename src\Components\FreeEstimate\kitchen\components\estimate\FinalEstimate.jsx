import React from 'react'

const FinalEstimate = () => {
    const kitchenSize = '10x12 ft';
    const material = 'HDFHMR';
    const style = 'Premium';
    const estimatedCost = '₹2,45,000';

    return (
        <div>
            <div className="max-w-xl mb-5 mx-auto bg-richblack-800 text-richblack-25 rounded-xl shadow-lg p-8 mt-12">
                <h2 className="text-2xl font-bold mb-6 text-center text-yellow-50">Final Estimate</h2>

                <div className="space-y-4">
                    <div className="flex justify-between">
                        <span className="font-medium text-richblack-100">Kitchen Size:</span>
                        <span>{kitchenSize}</span>
                    </div>

                    <div className="flex justify-between">
                        <span className="font-medium text-richblack-100">Material Selected:</span>
                        <span>{material}</span>
                    </div>

                    <div className="flex justify-between">
                        <span className="font-medium text-richblack-100">Interior Style:</span>
                        <span>{style}</span>
                    </div>

                    <div className="flex justify-between border-t border-richblack-600 pt-4 mt-4">
                        <span className="font-semibold text-yellow-100 text-lg">Total Estimated Cost:</span>
                        <span className="text-yellow-100 text-lg font-semibold">{estimatedCost}</span>
                    </div>
                </div>

                <p className="mt-6 text-center text-richblack-300 text-sm">
                    This estimate includes modular cabinets, premium finishes, installation and hardware.
                </p>

                <div className="mt-8 text-center">
                    <button
                        className="bg-yellow-100 hover:bg-yellow-200 text-richblack-800 font-semibold px-6 py-3 rounded-full transition-all duration-300"
                        onClick={() => window.location.href = '/contact'}
                    >
                        Contact Us
                    </button>
                </div>
            </div>
        </div>
    )
}

export default FinalEstimate;
