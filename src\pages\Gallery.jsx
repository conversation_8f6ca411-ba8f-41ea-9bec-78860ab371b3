import { useState, useEffect } from 'react';
import Navbar from '../Components/Common/Navbar';
import { Footer } from '../Components/Common/Footer';
import Breadcrumb from '../Components/Common/Breadcrumb';
import { FaSearch, FaTimes, FaChevronLeft, FaChevronRight, FaInstagram, FaFacebookF, FaWhatsapp } from 'react-icons/fa';
import { Link, useParams, useNavigate } from 'react-router-dom';

// Import dummy images
import dummyImage1 from '../assets/Images/About1.webp';
import dummyImage2 from '../assets/Images/About2.webp';
import dummyImage3 from '../assets/Images/About3.webp';
import dummyImage4 from '../assets/Images/Story.png';

const Gallery = () => {
    const { category } = useParams();
    const navigate = useNavigate();
    const [selectedCategory, setSelectedCategory] = useState(category || 'all');
    const [searchQuery, setSearchQuery] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [selectedImage, setSelectedImage] = useState(null);
    const [currentImageIndex, setCurrentImageIndex] = useState(0);

    // Update selected subcategory when URL parameter changes
    useEffect(() => {
        if (category) {
            setSelectedCategory(category);
        }
    }, [category]);

    // Handle subcategory change
    const handleCategoryChange = (newSubcategory) => {
        setSelectedCategory(newSubcategory);
        navigate(`/gallery/${newSubcategory === 'all' ? '' : newSubcategory}`);
    };

    // Gallery items with before and after images
    const galleryItems = [
        {
            id: 1,
            category: 'before-after',
            subcategory: 'living-room',
            beforeImage: dummyImage1,
            afterImage: dummyImage2,
            title: 'Modern Living Room Transformation',
            location: 'Mumbai, Maharashtra',
            date: 'March 2023'
        },
        {
            id: 2,
            category: 'before-after',
            subcategory: 'kitchen',
            beforeImage: dummyImage3,
            afterImage: dummyImage4,
            title: 'Luxury Kitchen Renovation',
            location: 'Delhi, NCR',
            date: 'January 2023'
        },
        {
            id: 3,
            category: 'before-after',
            subcategory: 'bedroom',
            beforeImage: dummyImage2,
            afterImage: dummyImage1,
            title: 'Master Bedroom Makeover',
            location: 'Bangalore, Karnataka',
            date: 'February 2023'
        },
        {
            id: 4,
            category: 'before-after',
            subcategory: 'bathroom',
            beforeImage: dummyImage4,
            afterImage: dummyImage3,
            title: 'Bathroom Remodeling',
            location: 'Pune, Maharashtra',
            date: 'April 2023'
        },
        {
            id: 5,
            category: 'before-after',
            subcategory: 'dining',
            beforeImage: dummyImage1,
            afterImage: dummyImage4,
            title: 'Dining Area Redesign',
            location: 'Chennai, Tamil Nadu',
            date: 'May 2023'
        },
        {
            id: 6,
            category: 'before-after',
            subcategory: 'living-room',
            beforeImage: dummyImage2,
            afterImage: dummyImage3,
            title: 'Living Room Transformation',
            location: 'Hyderabad, Telangana',
            date: 'June 2023'
        },
        {
            id: 7,
            category: 'before-after',
            subcategory: 'kitchen',
            beforeImage: dummyImage3,
            afterImage: dummyImage4,
            title: 'Kitchen Makeover',
            location: 'Kolkata, West Bengal',
            date: 'July 2023'
        },
        {
            id: 8,
            category: 'before-after',
            subcategory: 'office',
            beforeImage: dummyImage4,
            afterImage: dummyImage1,
            title: 'Home Office Renovation',
            location: 'Ahmedabad, Gujarat',
            date: 'August 2023'
        },
        {
            id: 9,
            category: 'before-after',
            subcategory: 'bedroom',
            beforeImage: dummyImage1,
            afterImage: dummyImage2,
            title: 'Guest Bedroom Redesign',
            location: 'Jaipur, Rajasthan',
            date: 'September 2023'
        }
    ];

    // Filter gallery items based on selected room type (subcategory) and search query
    const filteredItems = galleryItems.filter(item => {
        const matchesCategory = selectedCategory === 'all' || item.subcategory === selectedCategory;
        const matchesSearch = searchQuery === '' ||
            item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.location.toLowerCase().includes(searchQuery.toLowerCase());
        return matchesCategory && matchesSearch;
    });

    // Open modal with selected image
    const openModal = (item, index) => {
        setSelectedImage(item);
        setCurrentImageIndex(index);
        setShowModal(true);
        document.body.style.overflow = 'hidden';
    };

    // Close modal
    const closeModal = () => {
        setShowModal(false);
        setSelectedImage(null);
        document.body.style.overflow = 'auto';
    };

    // Navigate to previous image in modal
    const prevImage = () => {
        const newIndex = (currentImageIndex - 1 + filteredItems.length) % filteredItems.length;
        setSelectedImage(filteredItems[newIndex]);
        setCurrentImageIndex(newIndex);
    };

    // Navigate to next image in modal
    const nextImage = () => {
        const newIndex = (currentImageIndex + 1) % filteredItems.length;
        setSelectedImage(filteredItems[newIndex]);
        setCurrentImageIndex(newIndex);
    };

    // Handle keyboard navigation in modal
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (!showModal) return;

            if (e.key === 'Escape') {
                closeModal();
            } else if (e.key === 'ArrowLeft') {
                prevImage();
            } else if (e.key === 'ArrowRight') {
                nextImage();
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [showModal, currentImageIndex]);

    return (
        <div className="bg-richblack-900 min-h-screen">
            <Navbar />
            <Breadcrumb />

            {/* Hero Section */}
            <div className="relative h-[300px] w-full overflow-hidden">
                <div className="absolute inset-0 bg-black/70 z-10"></div>
                <img
                    src={dummyImage1}
                    alt="Interior Design Gallery"
                    className="h-full w-full object-cover"
                />
                <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4">
                    <h1 className="text-4xl md:text-5xl font-inter font-bold text-white mb-4">
                        Before & After Gallery
                    </h1>
                    <p className="text-lg text-gray-200 max-w-2xl mb-6">
                        See the dramatic transformations we've created for our clients' spaces
                    </p>
        
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 py-12">
                {/* Room Type Filters */}
           

                {/* Gallery Grid - Only Before & After Images */}
                {filteredItems.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {filteredItems.map((item, index) => (
                            <div
                                key={item.id}
                                className="relative overflow-hidden h-80 cursor-pointer group"
                                onClick={() => openModal(item, index)}
                            >
                                <div className="grid grid-cols-2 h-full gap-1">
                                    {/* Before Image */}
                                    <div className="relative h-full">
                                        <img
                                            src={item.beforeImage || item.image}
                                            alt={`${item.title} Before`}
                                            className="w-full h-full object-cover"
                                        />
                                        <div className="absolute top-3 left-3 bg-white/80 text-richblack-900 px-3 py-1 text-sm font-bold rounded-sm border-2 border-white">
                                            Before
                                        </div>
                                    </div>

                                    {/* After Image */}
                                    <div className="relative h-full">
                                        <img
                                            src={item.afterImage || item.image}
                                            alt={`${item.title} After`}
                                            className="w-full h-full object-cover"
                                        />
                                        <div className="absolute top-3 left-3 bg-white/80 text-richblack-900 px-3 py-1 text-sm font-bold rounded-sm border-2 border-white">
                                            After
                                        </div>
                                    </div>
                                </div>

                                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                                    <h3 className="text-white text-lg font-medium">{item.title}</h3>
                                </div>

                                {/* Simple Hover Overlay */}
                                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="bg-richblack-800 rounded-lg p-8 text-center">
                        <h3 className="text-xl font-semibold text-white mb-2">No projects found</h3>
                        <p className="text-gray-400 mb-4">Try adjusting your search or filter criteria</p>
                        <button
                            onClick={() => {
                                setSelectedCategory('all');
                                setSearchQuery('');
                                navigate('/gallery');
                            }}
                            className="bg-gold-500 text-richblack-900 px-6 py-2 rounded-sm font-medium hover:bg-gold-400 transition-colors duration-300"
                        >
                            View All Projects
                        </button>
                    </div>
                )}
            </div>

            {/* Image Modal */}
            {showModal && selectedImage && (
                <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="relative w-full max-w-5xl">
                        {/* Close Button */}
                        <button
                            onClick={closeModal}
                            className="absolute -top-12 right-0 text-white hover:text-gold-400 transition-colors duration-300 z-10"
                            aria-label="Close modal"
                        >
                            <FaTimes className="text-2xl" />
                        </button>

                        {/* Navigation Buttons */}
                        <button
                            onClick={prevImage}
                            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-12 bg-black/50 text-white p-3 rounded-full hover:bg-gold-500 hover:text-richblack-900 transition-all duration-300"
                            aria-label="Previous image"
                        >
                            <FaChevronLeft />
                        </button>
                        <button
                            onClick={nextImage}
                            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-12 bg-black/50 text-white p-3 rounded-full hover:bg-gold-500 hover:text-richblack-900 transition-all duration-300"
                            aria-label="Next image"
                        >
                            <FaChevronRight />
                        </button>

                        {/* Image Container */}
                        <div className="bg-richblack-800 rounded-lg overflow-hidden">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                                {/* Before Image */}
                                <div className="relative">
                                    <img
                                        src={selectedImage.beforeImage}
                                        alt={`${selectedImage.title} Before`}
                                        className="w-full h-[50vh] object-cover rounded-lg"
                                    />
                                    <div className="absolute top-4 left-4 bg-white/80 text-richblack-900 px-4 py-2 text-base font-bold rounded-sm border-2 border-white">
                                        Before
                                    </div>
                                </div>

                                {/* After Image */}
                                <div className="relative">
                                    <img
                                        src={selectedImage.afterImage}
                                        alt={`${selectedImage.title} After`}
                                        className="w-full h-[50vh] object-cover rounded-lg"
                                    />
                                    <div className="absolute top-4 left-4 bg-white/80 text-richblack-900 px-4 py-2 text-base font-bold rounded-sm border-2 border-white">
                                        After
                                    </div>
                                </div>
                            </div>

                            {/* Image Info */}
                            <div className="p-6 border-t border-richblack-700">
                                <h3 className="text-2xl font-bold text-white mb-2">{selectedImage.title}</h3>
                                <div className="flex flex-wrap justify-between items-center">
                                    <div className="text-gray-400">
                                        <p>{selectedImage.location}</p>
                                        <p className="text-sm">{selectedImage.date}</p>
                                    </div>
                                    <div className="flex gap-3 mt-4 md:mt-0">
                                        <a href="#" className="bg-richblack-700 hover:bg-gold-500 text-white hover:text-richblack-900 p-2 rounded-full transition-all duration-300">
                                            <FaWhatsapp />
                                        </a>
                                        <a href="#" className="bg-richblack-700 hover:bg-gold-500 text-white hover:text-richblack-900 p-2 rounded-full transition-all duration-300">
                                            <FaFacebookF />
                                        </a>
                                        <a href="#" className="bg-richblack-700 hover:bg-gold-500 text-white hover:text-richblack-900 p-2 rounded-full transition-all duration-300">
                                            <FaInstagram />
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <Footer />
        </div>
    );
};

export default Gallery;
