
import { Link } from 'react-router-dom';
import Navbar from '../../Components/Common/Navbar';
import { Footer } from '../../Components/Common/Footer';
import Breadcrumb from '../../Components/Common/Breadcrumb';
import { FaCheck, FaPhone, FaEnvelope, FaHome, FaPalette, FaCog } from 'react-icons/fa';

// Import images
import headerImg from '../../assets/Images/Header_Image/16443.jpg';
import dummyImage1 from '../../assets/Images/About1.webp';
import dummyImage2 from '../../assets/Images/About2.webp';
import dummyImage3 from '../../assets/Images/About3.webp';

const CompleteInterior = () => {
  const features = [
    'Complete Design Planning',
    'Material Selection & Sourcing',
    '3D Visualization & Rendering',
    'Project Management',
    'Quality Installation',
    'Post-Completion Support',
    'Warranty Coverage',
    'Timely Delivery'
  ];

  const services = [
    {
      icon: <FaHome className="text-gold-500 text-2xl" />,
      title: 'Full Home Design',
      description: 'Complete interior design solution for your entire home from living room to bedrooms'
    },
    {
      icon: <FaPalette className="text-gold-500 text-2xl" />,
      title: 'Custom Design Solutions',
      description: 'Personalized design concepts tailored to your lifestyle and preferences'
    },
    {
      icon: <FaCog className="text-gold-500 text-2xl" />,
      title: 'Turnkey Execution',
      description: 'End-to-end project execution from design to final installation and handover'
    }
  ];

  const packages = [
    {
      id: 1,
      title: 'Essential Package',
      description: 'Perfect for budget-conscious homeowners looking for quality design',
      features: ['Basic Design Consultation', 'Material Selection', 'Project Coordination', '1 Year Warranty'],
      image: dummyImage1
    },
    {
      id: 2,
      title: 'Premium Package',
      description: 'Comprehensive solution with premium materials and finishes',
      features: ['Complete Design Planning', '3D Visualization', 'Premium Materials', 'Project Management', '2 Year Warranty'],
      image: dummyImage2
    },
    {
      id: 3,
      title: 'Luxury Package',
      description: 'Ultimate luxury experience with designer materials and custom solutions',
      features: ['Luxury Design Concepts', 'Designer Materials', 'Custom Furniture', 'Dedicated Project Manager', '3 Year Warranty'],
      image: dummyImage3
    }
  ];

  return (
    <div className="bg-richblack-900 min-h-screen">
      <Navbar />
      <Breadcrumb />

      {/* Hero Section */}
      <div className="relative h-[400px] w-full overflow-hidden">
        <div className="absolute inset-0 bg-black/70 z-10"></div>
        <img
          src={headerImg}
          alt="Complete Interior Package"
          className="h-full w-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4">
          <h1 className="text-3xl md:text-5xl font-inter font-bold text-white mb-4">
            Complete Interior Package
          </h1>
          <p className="text-lg text-gray-200 max-w-3xl mb-6">
            Comprehensive interior designing and execution services for complete home transformation
          </p>
          <Link
            to="/contact"
            className="px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
          >
            Get Quote
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        {/* Service Overview */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold text-white mb-6">
              Complete Home Transformation
            </h2>
            <p className="text-gray-300 mb-6">
              Our Complete Interior Package offers a comprehensive solution for transforming your 
              entire home. From initial design concepts to final execution, we handle every aspect 
              of your interior design project with precision and care.
            </p>
            <p className="text-gray-300 mb-8">
              With our experienced team of designers and craftsmen, we ensure that every detail 
              is perfectly executed to create spaces that are both beautiful and functional.
            </p>
            
            {/* Features List */}
            <div className="grid grid-cols-2 gap-3">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <FaCheck className="text-gold-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-6">
            <img
              src={dummyImage1}
              alt="Interior Design Process"
              className="w-full h-64 object-cover rounded-lg"
            />
            <div className="bg-richblack-800 p-6 rounded-lg">
              <h3 className="text-xl font-bold text-white mb-4">What's Included</h3>
              <ul className="space-y-3 text-gray-300">
                <li>• Complete design planning and consultation</li>
                <li>• 3D visualization and walkthroughs</li>
                <li>• Material selection and procurement</li>
                <li>• Professional installation and execution</li>
                <li>• Quality assurance and project management</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Our Service Approach
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-richblack-800 p-6 rounded-lg text-center hover:bg-richblack-700 transition-all duration-300">
                <div className="mb-4 flex justify-center">
                  {service.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-300 text-sm">
                  {service.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Package Options */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Choose Your Package
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {packages.map((pkg) => (
              <div key={pkg.id} className="bg-richblack-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-richblack-700 hover:border-gold-500">
                <img
                  src={pkg.image}
                  alt={pkg.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-3">
                    {pkg.title}
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    {pkg.description}
                  </p>
                  <ul className="space-y-2 mb-6">
                    {pkg.features.map((feature, index) => (
                      <li key={index} className="text-gray-300 text-sm flex items-center">
                        <FaCheck className="text-gold-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <button className="w-full bg-gold-500 text-richblack-900 py-2 px-4 rounded font-medium hover:bg-gold-400 transition-colors duration-300">
                    Get Quote
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Process Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            Our Process
          </h2>
          <div className="grid md:grid-cols-5 gap-6">
            {[
              { step: '01', title: 'Consultation', desc: 'Initial meeting to understand your needs' },
              { step: '02', title: 'Design', desc: 'Creating detailed design concepts' },
              { step: '03', title: 'Planning', desc: 'Material selection and project planning' },
              { step: '04', title: 'Execution', desc: 'Professional installation and implementation' },
              { step: '05', title: 'Handover', desc: 'Final inspection and project completion' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gold-500 text-richblack-900 rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4">
                  {item.step}
                </div>
                <h3 className="text-lg font-bold text-white mb-2">{item.title}</h3>
                <p className="text-gray-300 text-sm">{item.desc}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-richblack-800 to-richblack-900 rounded-lg p-8 md:p-12 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Ready to Transform Your Home?</h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Get a personalized quote for your complete interior package. Our experts will help you choose the perfect solution for your space.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="inline-flex items-center gap-2 px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
            >
              <FaPhone className="text-sm" />
              Get Free Quote
            </Link>
            <Link
              to="/appointment"
              className="inline-flex items-center gap-2 px-8 py-3 bg-transparent border border-gold-500 text-gold-500 rounded-sm font-medium hover:bg-gold-500/10 transition-all duration-300"
            >
              <FaEnvelope className="text-sm" />
              Schedule Consultation
            </Link>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default CompleteInterior;
