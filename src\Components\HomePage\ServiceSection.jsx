
import { Link } from 'react-router-dom';
import { FaArrowRight } from 'react-icons/fa';
import modularKitchenImg from '../../assets/Images/Services/modular-kitchen-wardrobes.jpg';
import completeInteriorImg from '../../assets/Images/Services/complete-interior-package.jpg';
import commercialResidentialImg from '../../assets/Images/Services/commercial-residential-designing.png';
import fullHomeInteriorImg from '../../assets/Images/Services/complete-interior-package.jpg';

export const ServiceSection = () => {
  const services = [
    {
      id: 1,
      title: 'Modular Kitchen & Wardrobes',
      description: 'Machine-made modular solutions for kitchens and wardrobes with premium quality materials and modern designs.',
      img: modularKitchenImg,
      link: '/services/modular-kitchen-wardrobes'
    },
    {
      id: 2,
      title: 'Complete Interior Package',
      description: 'Comprehensive interior designing and execution services for complete home transformation.',
      img: completeInteriorImg,
      link: '/services/complete-interior'
    },
    {
      id: 3,
      title: 'Commercial & Residential Designing',
      description: 'Professional design services for both commercial spaces and residential properties.',
      img: commercialResidentialImg,
      link: '/services/commercial-residential'
    },
    {
      id: 4,
      title: 'Full Home Interior',
      description: 'Complete home interior solutions from concept to completion with expert craftsmanship.',
      img: fullHomeInteriorImg,
      link: '/services/full-home-interior'
    }
  ];



  return (
    <section className="py-16 bg-richblack-900 text-white">
      <div className="max-w-6xl mx-auto px-4">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center">
            <span className="w-10 h-1 bg-gold-500 mr-4 inline-block"></span>
            Our Premium Services
            <span className="w-10 h-1 bg-gold-500 ml-4 inline-block"></span>
          </h2>
          <p className="text-gray-300 max-w-3xl mx-auto">
            Discover our comprehensive range of interior design services tailored to transform your space into a stunning reflection of your style and personality.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {services.map((service) => (
            <Link
              key={service.id}
              to={service.link}
              className="bg-richblack-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group border border-richblack-700 hover:border-gold-500 block"
            >
              <div className="relative overflow-hidden">
                {/* Service Image */}
                <img
                  src={service.img}
                  alt={service.title}
                  className="w-full h-64 object-cover transform transition-transform duration-500 group-hover:scale-105"
                />

                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center p-4">
                  <div className="bg-gold-500 text-richblack-900 px-4 py-2 rounded-sm font-medium">
                    Learn More
                  </div>
                </div>
              </div>

              <div className="p-5">
                {/* Service Title */}
                <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-gold-400 transition-colors duration-300">
                  {service.title}
                </h3>
              </div>
            </Link>
          ))}
        </div>

        {/* View All Services Button */}
        <div className="text-center">
          <Link
            to="/services"
            className="inline-flex items-center gap-2 px-8 py-3 bg-gold-500 hover:bg-gold-400 text-richblack-900 font-medium rounded-sm transition-all duration-300"
          >
            View All Services <FaArrowRight className="text-xs" />
          </Link>
        </div>
      </div>
    </section>
  );
};
