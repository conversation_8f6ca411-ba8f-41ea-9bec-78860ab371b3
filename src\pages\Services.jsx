
import { Link } from 'react-router-dom';
import Navbar from '../Components/Common/Navbar';
import { Footer } from '../Components/Common/Footer';
import Breadcrumb from '../Components/Common/Breadcrumb';

// Import service images
import commercialResidentialImg from '../assets/Images/Services/commercial-residential-designing.png';
import landscapingImg from '../assets/Images/Services/street-garden-landscaping.jpg';
import completeInteriorImg from '../assets/Images/Services/complete-interior-package.jpg';
import modularKitchenImg from '../assets/Images/Services/modular-kitchen-wardrobes.jpg';
import architectsImg from '../assets/Images/Services/architects-plan-layout.jpg';
import paintingImg from '../assets/Images/Services/painting-floor-tiling.jpg';
import ceilingImg from '../assets/Images/Services/false-ceiling-electrical.jpg';
import furnitureImg from '../assets/Images/Services/furniture-office-infrastructure.jpg';
import turnkeyImg from '../assets/Images/Services/turnkey-construction-projects.jpg';
import upholsteryImg from '../assets/Images/Services/upholstery-services.jpg';
import headerImg from '../assets/Images/Header_Image/150295.jpg';

const Services = () => {
  const services = [
    {
      id: 1,
      title: 'Commercial & Residential Designing',
      description: 'Professional design services for both commercial spaces and residential properties with expert planning and execution.',
      image: commercialResidentialImg,
      link: '/services/commercial-residential'
    },
    {
      id: 2,
      title: 'Street & Garden Landscaping',
      description: 'Beautiful outdoor spaces with professional landscaping design for streets, gardens, and outdoor areas.',
      image: landscapingImg,
      link: '/services/landscaping'
    },
    {
      id: 3,
      title: 'Complete Interior Package',
      description: 'Comprehensive interior designing and execution services for complete home and office transformation.',
      image: completeInteriorImg,
      link: '/services/complete-interior'
    },
    {
      id: 4,
      title: 'Modular Kitchen & Wardrobes',
      description: 'Machine-made modular solutions for kitchens and wardrobes with premium quality materials and modern designs.',
      image: modularKitchenImg,
      link: '/services/modular-kitchen-wardrobes'
    },
    {
      id: 5,
      title: 'Architects & Plan Layout',
      description: 'Professional architectural services with structural engineering for comprehensive building solutions.',
      image: architectsImg,
      link: '/services/architects-plan-layout'
    },
    {
      id: 6,
      title: 'Painting & Floor Tiling',
      description: 'Professional painting and tiling services for interior and exterior with premium quality materials.',
      image: paintingImg,
      link: '/services/painting-tiling'
    },
    {
      id: 7,
      title: 'False Ceiling & Electrical',
      description: 'Modern false ceiling designs and comprehensive electrical solutions for enhanced aesthetics.',
      image: ceilingImg,
      link: '/services/ceiling-electrical'
    },
    {
      id: 8,
      title: 'Furniture & Office Infrastructure',
      description: 'High-quality furniture solutions and complete office setup for all spaces.',
      image: furnitureImg,
      link: '/services/furniture-office'
    },
    {
      id: 9,
      title: 'Turnkey & Construction Projects',
      description: 'End-to-end construction and turnkey project solutions from planning to completion.',
      image: turnkeyImg,
      link: '/services/turnkey-construction'
    },
    {
      id: 10,
      title: 'Upholstery Services',
      description: 'Professional upholstery services for furniture restoration and custom fabric solutions.',
      image: upholsteryImg,
      link: '/services/upholstery'
    }
  ];

  return (
    <div className="bg-richblack-900 min-h-screen">
      <Navbar />
      <Breadcrumb />

      {/* Hero Section */}
      <div className="relative h-[400px] w-full overflow-hidden">
        <div className="absolute inset-0 bg-black/70 z-10"></div>
        <img
          src={headerImg}
          alt="Our Services"
          className="h-full w-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4">
          <h1 className="text-3xl md:text-5xl font-inter font-bold text-white mb-4">
            Our Services
          </h1>
          <p className="text-lg text-gray-200 max-w-3xl mb-6">
            India's Best Interior Design Company offering comprehensive solutions for all your design and construction needs
          </p>
        </div>
      </div>

      {/* Services Grid */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">
            Complete Interior & Construction Solutions
          </h2>
          <p className="text-gray-300 max-w-2xl mx-auto">
            From design to execution, we provide end-to-end services for residential, commercial, and industrial projects
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => (
            <Link
              key={service.id}
              to={service.link}
              className="bg-richblack-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group border border-richblack-700 hover:border-gold-500"
            >
              <div className="relative overflow-hidden">
                <img
                  src={service.image}
                  alt={service.title}
                  className="w-full h-48 object-cover transform transition-transform duration-500 group-hover:scale-105"
                />
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-white mb-3 group-hover:text-gold-400 transition-colors duration-300">
                  {service.title}
                </h3>
                <p className="text-gray-300 text-sm">
                  {service.description}
                </p>
              </div>
            </Link>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-richblack-800 to-richblack-900 rounded-lg p-8 md:p-12">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Transform Your Space?</h2>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              Contact us today for a free consultation and let our experts bring your vision to life
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/contact"
                className="px-8 py-3 bg-gold-500 text-richblack-900 rounded-sm font-medium hover:bg-gold-400 transition-all duration-300"
              >
                Get Free Consultation
              </Link>
              <a
                href="tel:9599222158"
                className="px-8 py-3 bg-transparent border border-gold-500 text-gold-500 rounded-sm font-medium hover:bg-gold-500/10 transition-all duration-300"
              >
                Call Now: 9599222158
              </a>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Services;
