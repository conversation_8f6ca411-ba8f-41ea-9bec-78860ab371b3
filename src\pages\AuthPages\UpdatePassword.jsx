import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>Eye, AiOutlineEyeInvisible } from "react-icons/ai";
import { BiArrowBack } from "react-icons/bi";
import { Link, useNavigate } from "react-router-dom";
import Navbar from "../../Components/Common/Navbar";

const UpdatePassword = () => {
    const navigate = useNavigate();
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const handleOnSubmit = async (e) => {
        e.preventDefault();

        if (password !== confirmPassword) {
            alert("Passwords do not match.");
            return;
        }

        try {
            // Simulated password update - replace with your actual API call
            console.log("✅ Password updated:", password);
            alert("Password updated successfully!");
            navigate("/login");
        } catch (error) {
            console.error("Error updating password:", error);
        }
    };

    return (
        <>
        <Navbar/>
        <div className="grid min-h-[calc(100vh-3.5rem)] place-items-center bg-richblack-900 p-4">
            <div className="w-full max-w-[500px] rounded-2xl bg-richblack-800 p-6 shadow-[0_10px_30px_rgba(0,0,0,0.3)] lg:p-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-richblack-5">Choose new password</h1>
                    <p className="mt-3 text-lg text-richblack-100">
                        Almost done. Enter your new password and you're all set.
                    </p>
                </div>

                <form onSubmit={handleOnSubmit} className="space-y-6">
                    {/* New Password Field */}
                    <label className="block group relative z-0 w-full">
                        <span className="mb-2 block text-sm font-medium text-richblack-300 transition-all duration-200 group-focus-within:text-yellow-400">
                            New Password <sup className="text-pink-200">*</sup>
                        </span>

                        <div className="relative">
                            <input
                                required
                                type={showPassword ? "text" : "password"}
                                name="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                className="peer block w-full rounded-xl border border-richblack-600 bg-richblack-700 px-4 pb-1 pt-6 text-richblack-5 
                          shadow-[0_4px_10px_rgba(0,0,0,0.25)] transition-all duration-200
                          focus:border-yellow-400 focus:outline-none focus:ring-2 focus:ring-yellow-500/30
                          hover:border-richblack-400"
                            />

                 

                            <button
                                type="button"
                                onClick={() => setShowPassword((prev) => !prev)}
                                className="absolute right-3 top-1/2 -translate-y-1/2 text-xl text-richblack-400 hover:text-richblack-100 transition-colors"
                            >
                                {showPassword ? <AiOutlineEyeInvisible /> : <AiOutlineEye />}
                            </button>

                            
                        </div>
                    </label>

                    {/* Confirm Password Field */}
                    <label className="block group relative z-0 w-full">
                        <span className="mb-2 block text-sm font-medium text-richblack-300 transition-all duration-200 group-focus-within:text-yellow-400">
                            Confirm New Password <sup className="text-pink-200">*</sup>
                        </span>

                        <div className="relative">
                            <input
                                required
                                type={showConfirmPassword ? "text" : "password"}
                                name="confirmPassword"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                className="peer block w-full rounded-xl border border-richblack-600 bg-richblack-700 px-4 pb-1 pt-6 text-richblack-5 
                          shadow-[0_4px_10px_rgba(0,0,0,0.25)] transition-all duration-200
                          focus:border-yellow-400 focus:outline-none focus:ring-2 focus:ring-yellow-500/30
                          hover:border-richblack-400"
                            />

                    

                            <button
                                type="button"
                                onClick={() => setShowConfirmPassword((prev) => !prev)}
                                className="absolute right-3 top-1/2 -translate-y-1/2 text-xl text-richblack-400 hover:text-richblack-100 transition-colors"
                            >
                                {showConfirmPassword ? <AiOutlineEyeInvisible /> : <AiOutlineEye />}
                            </button>

                           
                        </div>
                    </label>

                    <button
                        type="submit"
                        className="w-full rounded-xl bg-yellow-400 py-3.5 font-bold text-richblack-900 shadow-[0_4px_15px_rgba(255,199,0,0.25)] 
                      transition-all duration-300 hover:scale-[1.02] hover:bg-yellow-300 hover:shadow-[0_6px_20px_rgba(255,199,0,0.4)] 
                      focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
                    >
                        Reset Password
                    </button>
                </form>

                <div className="mt-8">
                    <Link
                        to="/login"
                        className="flex items-center gap-x-2 font-medium text-richblack-100 transition hover:text-yellow-300"
                    >
                        <BiArrowBack className="text-lg" /> Back To Login
                    </Link>
                </div>
            </div>
        </div>
        </>

    );
};

export default UpdatePassword;