import { Route, Routes } from "react-router-dom"
import HeightSelection from "./HeightSelection"
import Types from "./types"
import MaterialTypes from "./MaterialTypes"
import Accessories from "./Accessories"
import { FinalEstimated } from "./FinalEstimated"

export const WardrobeRoues=()=>{
    return(

        <Routes>
            <Route path="/HeightSelection" element={<HeightSelection/>} ></Route>
            <Route path="/HeightSelection/types" element={<Types/>} ></Route>
            <Route path="/HeightSelection/types/materialtype" element={<MaterialTypes/>} ></Route>
            <Route path="/HeightSelection/types/materialtype/accessories" element={<Accessories/>} ></Route>
            <Route path="/HeightSelection/types/materialtype/accessories/estimated" element={<FinalEstimated/>} ></Route>

        </Routes>
    )
}
