import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from "lucide-react";
import { Link } from 'react-router-dom';

export const BhkSelection = ({setstep}) => {
    const [selectedBHK, setSelectedBHK] = useState(null);
    const [openDropdown, setOpenDropdown] = useState(null);

    const bhkOptions = [
        { label: "1 BHK", value: "1bhk" },
        { label: "2 BHK", value: "2bhk" },
        { label: "3 BHK", value: "3bhk" },
        { label: "4 BHK", value: "4bhk" },
        { label: "5 BHK+", value: "5bhkplus" },
    ];

    const handleSelect = (value) => {
        setSelectedBHK(value);
    };

    const toggleDropdown = (value) => {
        setOpenDropdown(openDropdown === value ? null : value);
    };

    return (
        <div className="p-6 max-w-md mx-auto">
            <h2 className="text-3xl text-richblack-50 font-semibold font-inter  text-center">Select your BHK type</h2>
     

            <div className="grid grid-cols-2 gap-4 mt-6">
                {bhkOptions.map((bhk) => (
                    <div
                        key={bhk.value}
                        className={`p-5 rounded-xl border cursor-pointer relative flex items-center justify-center text-center transition-all ${
                            selectedBHK === bhk.value
                                ? "border-yellow-300 bg-pink-50 shadow-md"
                                : "border-gray-300 bg-white"
                        }`}
                        onClick={() => handleSelect(bhk.value)}
                    >
                        <span className="text-base font-medium">{bhk.label}</span>

                        {bhk.value !== "1bhk" && (
                            <div
                                className="absolute top-2 right-2"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    toggleDropdown(bhk.value);
                                }}
                            >
                                {openDropdown === bhk.value ? (
                                    <ChevronUp className="w-4 h-4 text-gray-500" />
                                ) : (
                                    <ChevronDown className="w-4 h-4 text-gray-500" />
                                )}
                            </div>
                        )}

                        {openDropdown === bhk.value && (
                            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-xs text-gray-500">
                                Extra info can go here
                            </div>
                        )}
                    </div>
                ))}
            </div>

            <div className="flex justify-between mt-6">
                <button className="text-sm text-yellow-300">BACK</button>
            <Link to='roomselection'>
            <button onClick={()=>{
                setstep(1)
            }}
                    className="px-6 py-2 rounded-full bg-yellow-300 text-white"
                    disabled={!selectedBHK}
                >
                    NEXT
                </button>
            </Link>
            </div>
        </div>
    );
};
